# Boltz Potentials 시스템 종합 분석

## 목차

1. [서론](#1-서론)
2. [이론적 배경](#2-이론적-배경)
3. [시스템 아키텍처](#3-시스템-아키텍처)
4. [Potential 클래스 분석](#4-potential-클래스-분석)
5. [물리적 의미와 구현](#5-물리적-의미와-구현)
6. [사용처 및 통합](#6-사용처-및-통합)
7. [결론](#7-결론)

---

## 1. 서론

### 1.1 문서의 목적

이 문서는 Boltz-2 프로젝트의 `potentials.py` 모듈을 체계적으로 분석하여, 분자 구조 예측에서 물리적 제약 조건이 어떻게 구현되고 활용되는지를 설명합니다.

### 1.2 Chain of Thought 접근법

본 분석은 다음과 같은 논리적 단계를 따릅니다:
1. **이론적 기반 이해** → 분자 역학의 제약 조건과 포텐셜 함수
2. **코드 구조 파악** → 추상화와 상속 관계 분석
3. **물리적 의미 연결** → 코드와 물리 현상의 연관성
4. **통합적 역할 이해** → 전체 시스템에서의 위치와 기능

---

## 2. 이론적 배경

### 2.1 분자 역학에서의 제약 조건

#### 2.1.1 기본 개념

분자 구조 예측에서 제약 조건(constraints)은 화학적으로 합리적인 구조를 보장하기 위해 필수적입니다:

```python
# 물리적 제약의 예: 결합 길이 제한
bond_length_constraint = "1.2 Å ≤ C-C bond ≤ 1.6 Å"
```

#### 2.1.2 포텐셜 에너지 함수

포텐셜 에너지는 분자 시스템의 안정성을 나타내며, 다음과 같은 형태를 가집니다:

**E_total = E_bond + E_angle + E_dihedral + E_nonbonded + E_constraints**

### 2.2 Flat-Bottom Potential의 물리적 의미

Flat-bottom potential은 허용 범위 내에서는 에너지 페널티가 없고, 범위를 벗어날 때만 선형적으로 증가하는 포텐셜입니다:

```
Energy
  ^
  |     /
  |    /
  |___/________\___ 
  |              \
  |               \
  +---|-------|-----> Distance
     min     max
```

---

## 3. 시스템 아키텍처

### 3.1 클래스 계층 구조

```python
# 추상 기본 클래스
class Potential(ABC):
    """
    Base class for all potential functions.
    Provides common interface and logging capabilities.
    """
    
# 구체적인 포텐셜 함수들
class FlatBottomPotential(Potential)      # 플랫 바텀 포텐셜
class DistancePotential(Potential)        # 거리 기반 제약
class DihedralPotential(Potential)        # 이면각 제약
```

### 3.2 핵심 메서드 분석

#### 3.2.1 `compute()` 메서드 - 에너지 계산의 핵심

```python
def compute(self, coords, feats, parameters):
    """
    Calculate potential energy for given coordinates.
    
    Args:
        coords: Atomic coordinates [batch, atoms, 3]
        feats: Feature dictionary containing constraint data
        parameters: Time-dependent parameters
    
    Returns:
        Energy tensor [batch]
    """
    # 1. 제약 조건 설정
    index, args, com_args = self.compute_args(feats, parameters)
    
    # 2. 제약이 없으면 제로 에너지 반환
    if index.shape[1] == 0:
        return torch.zeros(coords.shape[:-2], device=coords.device)
    
    # 3. Center of Mass 처리 (필요시)
    if com_args is not None:
        coords = self._process_center_of_mass(coords, com_args)
    
    # 4. 기하학적 변수 계산 (거리, 각도 등)
    value = self.compute_variable(coords, index, compute_gradient=False)
    
    # 5. 에너지 함수 적용
    energy = self.compute_function(value, *args)
    
    return energy.sum(dim=-1)
```

**물리적 의미**: 이 메서드는 현재 분자 구조에서 제약 조건 위반에 따른 에너지 페널티를 계산합니다.

#### 3.2.2 `compute_gradient()` 메서드 - 힘 계산

```python
def compute_gradient(self, coords, feats, parameters):
    """
    Calculate forces (negative gradients) on atoms.
    
    Physical meaning: F = -∇E
    """
    # 에너지의 음의 기울기가 곧 힘
    value, grad_value = self.compute_variable(coords, index, compute_gradient=True)
    energy, dEnergy = self.compute_function(value, *args, compute_derivative=True)
    
    # 연쇄법칙을 사용한 기울기 계산
    grad_atom = self._apply_chain_rule(dEnergy, grad_value, index)
    
    return grad_atom
```

**물리적 의미**: 각 원자에 작용하는 힘을 계산하여 구조 최적화에 활용됩니다.

---

## 4. Potential 클래스 분석

### 4.1 DistancePotential - 거리 제약

```python
class DistancePotential(Potential):
    def compute_variable(self, coords, index, compute_gradient=False):
        """
        Calculate pairwise distances between atoms.
        
        Physical meaning: |r_i - r_j|
        """
        r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
        r_ij_norm = torch.linalg.norm(r_ij, dim=-1)
        
        if not compute_gradient:
            return r_ij_norm
            
        # 단위 벡터 계산 (기울기 방향)
        r_hat_ij = r_ij / r_ij_norm.unsqueeze(-1)
        grad_i = r_hat_ij      # ∂|r_ij|/∂r_i
        grad_j = -r_hat_ij     # ∂|r_ij|/∂r_j
        
        return r_ij_norm, torch.stack((grad_i, grad_j), dim=1)
```

**물리적 의미**: 
- 결합 길이 제약 (bond length constraints)
- Van der Waals 충돌 방지
- 수소 결합 거리 유지

### 4.2 DihedralPotential - 이면각 제약

```python
class DihedralPotential(Potential):
    def compute_variable(self, coords, index, compute_gradient=False):
        """
        Calculate dihedral angles φ between four atoms i-j-k-l.
        
        Physical meaning: Torsional angle around j-k bond
        """
        # 벡터 정의
        r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
        r_kj = coords.index_select(-2, index[2]) - coords.index_select(-2, index[1])
        r_kl = coords.index_select(-2, index[2]) - coords.index_select(-2, index[3])
        
        # 법선 벡터 계산
        n_ijk = torch.cross(r_ij, r_kj, dim=-1)  # 평면 i-j-k의 법선
        n_jkl = torch.cross(r_kj, r_kl, dim=-1)  # 평면 j-k-l의 법선
        
        # 이면각 계산: φ = arccos(n_ijk · n_jkl / |n_ijk||n_jkl|)
        cos_phi = (n_ijk @ n_jkl) / (|n_ijk| * |n_jkl|)
        phi = torch.arccos(torch.clamp(cos_phi, -1+ε, 1-ε))
        
        return phi
```

**물리적 의미**: 
- 단백질의 백본 이면각 (φ, ψ)
- 분자의 형태적 선호도
- 회전 장벽 모델링

### 4.3 특수한 제약 조건들

#### 4.3.1 NMRDistancePotential - NMR 거리 제약

```python
class NMRDistancePotential(FlatBottomPotential, DistancePotential):
    """
    NMR-derived distance constraints with both upper and lower bounds.
    
    Physical background: NOE (Nuclear Overhauser Effect) data provides
    distance information between atom pairs in solution.
    """
    
    def compute_args(self, feats, parameters):
        # NMR 실험 데이터에서 얻은 거리 정보
        pair_index = feats["nmr_distance_atom_index"][0]
        lower_bounds = feats["nmr_distance_lower_bounds"][0]
        upper_bounds = feats["nmr_distance_upper_bounds"][0]
        weights = feats["nmr_distance_weights"][0]
        
        # 실험적 불확실성을 고려한 버퍼 적용
        lower_bounds *= (1.0 - parameters["lower_buffer"])
        upper_bounds[finite_mask] *= (1.0 + parameters["upper_buffer"])
        
        # 신뢰도에 따른 힘 상수 조정
        k = weights * parameters["base_force_constant"]
        
        return pair_index, (k, lower_bounds, upper_bounds), None
```

**물리적 의미**: NMR 실험에서 얻은 원자간 거리 정보를 구조 예측에 활용

#### 4.3.2 ChiralAtomPotential - 키랄성 제약

```python
class ChiralAtomPotential(FlatBottomPotential, DihedralPotential):
    """
    Enforce chirality constraints on tetrahedral centers.
    
    Physical meaning: Stereochemical configuration (R/S)
    """
    
    def compute_args(self, feats, parameters):
        chiral_atom_index = feats["chiral_atom_index"][0]
        chiral_orientations = feats["chiral_atom_orientations"][0].bool()
        
        # R 형태: 양의 이면각 요구
        lower_bounds[chiral_orientations] = parameters["buffer"]
        upper_bounds[chiral_orientations] = float("inf")
        
        # S 형태: 음의 이면각 요구  
        upper_bounds[~chiral_orientations] = -parameters["buffer"]
        lower_bounds[~chiral_orientations] = float("-inf")
        
        return chiral_atom_index, (k, lower_bounds, upper_bounds), None
```

**물리적 의미**: 분자의 입체화학적 정확성 보장 (R/S 형태 구분)

---

## 5. 물리적 의미와 구현

### 5.1 에너지 기반 Guidance 시스템

#### 5.1.1 Diffusion Sampling과의 통합

```python
# diffusion.py에서의 활용
def sample(self, atom_mask, steering_args=None, **kwargs):
    """
    Diffusion sampling with potential-based guidance.
    """
    for step_idx in range(num_sampling_steps):
        # 1. 일반적인 diffusion step
        x_pred = self.diffusion_step(x_noisy, sigma, **kwargs)
        
        # 2. Potential-based guidance
        if steering_args["guidance_update"]:
            for potential in self.potentials:
                # 제약 조건 위반에 따른 에너지 계산
                energy = potential.compute(x_pred, feats, parameters)
                
                # 에너지 기울기로 구조 조정
                grad = potential.compute_gradient(x_pred, feats, parameters)
                x_pred = x_pred - step_size * grad
                
        # 3. FK Resampling (에너지 기반 필터링)
        if steering_args["fk_steering"]:
            # 높은 에너지 구조 제거
            weights = torch.exp(-beta * total_energy)
            x_pred = self.resample(x_pred, weights)
```

**물리적 의미**: 
- **Guidance**: 제약 조건을 만족하는 방향으로 샘플링 유도
- **Resampling**: 에너지가 높은 (부정확한) 구조들을 확률적으로 제거

#### 5.1.2 Parameter Scheduling의 역할

```python
class ExponentialInterpolation(ParameterSchedule):
    """
    Exponential interpolation for time-dependent parameters.
    
    Physical meaning: Gradually increasing constraint strength
    """
    def compute(self, t):
        # t=0 (초기): 약한 제약
        # t=1 (최종): 강한 제약
        return start + (end - start) * (exp(α*t) - 1) / (exp(α) - 1)
```

**물리적 의미**: 
- 초기에는 약한 제약으로 넓은 탐색 허용
- 점진적으로 제약 강화하여 정확한 구조로 수렴

### 5.2 실제 적용 사례

#### 5.2.1 PoseBustersPotential - 약물 설계

```python
class PoseBustersPotential(FlatBottomPotential, DistancePotential):
    """
    Enforce chemically reasonable geometries in drug molecules.
    """
    def compute_args(self, feats, parameters):
        # RDKit에서 계산된 화학적 제약들
        pair_index = feats["rdkit_bounds_index"][0]
        bond_mask = feats["rdkit_bounds_bond_mask"][0]      # 공유결합
        angle_mask = feats["rdkit_bounds_angle_mask"][0]    # 결합각
        
        # 제약 유형별 버퍼 적용
        lower_bounds[bond_mask] *= (1.0 - parameters["bond_buffer"])
        lower_bounds[angle_mask] *= (1.0 - parameters["angle_buffer"])
        lower_bounds[clash_mask] *= (1.0 - parameters["clash_buffer"])
```

**물리적 의미**: 
- **결합 길이**: 화학 결합의 적절한 거리 유지
- **결합각**: 분자 기하학적 선호도 반영
- **충돌 방지**: 원자간 최소 거리 보장

#### 5.2.2 VDWOverlapPotential - 입체 충돌

```python
class VDWOverlapPotential(FlatBottomPotential, DistancePotential):
    """
    Prevent steric clashes between non-bonded atoms.
    """
    def compute_args(self, feats, parameters):
        # Van der Waals 반지름 합계
        vdw_radii = const.vdw_radii  # 원소별 VdW 반지름
        atom_vdw_radii = feats["ref_element"] @ vdw_radii
        
        # 최소 허용 거리 = VdW 반지름 합 × (1 - buffer)
        min_distance = (atom_vdw_radii[i] + atom_vdw_radii[j]) * (1 - buffer)
```

**물리적 의미**: 
- Pauli 배제 원리에 의한 원자간 반발
- 분자 내 입체 충돌 방지
- 적절한 분자 밀도 유지

---

## 6. 사용처 및 통합

### 6.1 Diffusion Model과의 통합

#### 6.1.1 Training Phase
```python
# boltz2.py에서
def training_step(self, batch, batch_idx):
    """Include potential energy in training loss."""
    
    # 일반적인 diffusion loss
    diffusion_loss = self.diffusion_module.compute_loss(feats, out_dict)
    
    # Potential-based regularization (선택적)
    if self.use_potential_training:
        potential_loss = sum(
            potential.compute(pred_coords, feats, parameters) 
            for potential in self.potentials
        )
        total_loss = diffusion_loss + λ * potential_loss
```

#### 6.1.2 Inference Phase
```python
# diffusionv2.py에서
def sample(self, **kwargs):
    """Guided sampling with potentials."""
    potentials = get_potentials(debug_enabled=False)
    
    for step in sampling_schedule:
        # Standard denoising
        x_pred = self.denoise_step(x_noisy, sigma)
        
        # Potential guidance
        for potential in potentials:
            energy = potential.compute(x_pred, feats, params)
            if energy > threshold:
                grad = potential.compute_gradient(x_pred, feats, params)
                x_pred -= guidance_strength * grad
```

### 6.2 사용자 인터페이스

#### 6.2.1 CLI 인터페이스
```python
# main.py에서
@click.option("--use_potentials", is_flag=True)
def predict(use_potentials: bool, ...):
    """Enable/disable potential-based guidance."""
    
    steering_args = BoltzSteeringParams()
    steering_args.fk_steering = use_potentials
    steering_args.guidance_update = use_potentials
```

#### 6.2.2 설정 파일 통합
```yaml
# config.yaml
steering_args:
  fk_steering: true           # FK resampling 활성화
  guidance_update: true       # Gradient guidance 활성화
  fk_resampling_interval: 3   # 3 step마다 resampling
  num_gd_steps: 20           # Gradient descent steps
```

---

## 7. 결론

### 7.1 Potentials 시스템의 핵심 가치

Boltz의 potentials 시스템은 다음과 같은 핵심 가치를 제공합니다:

1. **화학적 정확성**: 물리적 제약을 통한 합리적 구조 생성
2. **모듈화**: 독립적인 제약 조건의 조합 가능
3. **적응적 제어**: 시간에 따른 매개변수 조정
4. **디버깅 지원**: 상세한 로깅과 통계 제공

### 7.2 물리학적 의의

이 시스템은 **분자 역학의 원리**를 **기계학습 모델**에 성공적으로 통합한 사례입니다:

- **에너지 최소화 원리**: 안정한 분자 구조로의 수렴
- **통계역학적 샘플링**: Boltzmann 분포 기반 구조 선택
- **다중 스케일 모델링**: 원자 수준부터 분자 수준까지의 제약

### 7.3 실용적 영향

1. **약물 설계**: 화학적으로 합성 가능한 분자 구조 생성
2. **단백질 구조 예측**: 실험적 제약 조건 통합
3. **재료 과학**: 특정 물성을 가진 분자 설계

### 7.4 향후 발전 방향

1. **동적 제약**: 시간에 따라 변하는 제약 조건
2. **학습된 포텐셜**: 데이터로부터 자동 학습되는 제약
3. **다중 스케일 통합**: 양자역학적 효과까지 포함

---

**이 분석을 통해 Boltz potentials 시스템이 단순한 제약 조건 이상의 의미를 가지며, 물리학과 기계학습의 성공적인 융합 사례임을 확인할 수 있습니다.** 
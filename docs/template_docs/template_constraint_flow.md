# Template Constraint Processing Flow

## 개요

이 문서는 Boltz 시스템에서 YAML 입력 파일로부터 template constraint가 어떻게 처리되는지 시각화합니다.

## 주요 구성 요소

### 1. TemplateConstraintGenerator 클래스
- **역할**: Template 구조로부터 distance constraint 생성
- **주요 기능**:
  - Cb-Cb distance constraint 생성
  - S-S disulfide bond constraint 생성 (NMR distance 및 bond constraint 모두 지원)
  - Sequence alignment 기반 mapping

### 2. SequenceMappingResult 데이터클래스
- **역할**: Template과 query sequence 간의 mapping 결과 저장
- **포함 정보**:
  - Aligned sequences
  - Residue mapping
  - Sequence identity
  - Mapping dictionary for fast lookup

### 3. 주요 메서드들

#### 구조적 메서드
- `_prepare_sequence_mapping()`: Sequence alignment 및 validation
- `_compute_distance_pairs()`: 일반화된 거리 계산 로직
- `_create_constraint_from_mapping()`: Constraint 객체 생성

#### Constraint 생성 메서드
- `generate_cb_cb_constraints()`: Cb-Cb constraint 전용
- `generate_disulfide_constraints_internal()`: S-S constraint 내부 로직
- `generate_template_constraints()`: 통합 constraint 생성 (진입점)

## 처리 흐름도

```mermaid
graph TD
    A["YAML Input File"] -->|parse_boltz_schema| B["Schema Parsing"]
    B --> C{"Check for<br/>sequences"}
    
    C -->|Yes| D["Extract protein<br/>template info"]
    D --> E{"Template info<br/>exists?"}
    
    E -->|Yes| F["Initialize<br/>TemplateConstraintGenerator"]
    E -->|No| G["Skip template<br/>constraints"]
    
    F --> H["For each chain<br/>with template"]
    H --> I["generate_template_constraints()"]
    
    I --> J["prepare_sequence_mapping()"]
    J --> K["Sequence alignment<br/>(Needleman-Wunsch)"]
    K --> L["Calculate sequence<br/>identity"]
    L --> M{"Identity ><br/>threshold?"}
    
    M -->|Yes| N["Generate Cb-Cb<br/>constraints"]
    M -->|No| O["Skip constraint<br/>generation"]
    
    N --> P["extract_cb_coordinates()"]
    P --> Q["compute_distance_pairs()<br/>(Cb-Cb filter)"]
    Q --> R["create_constraint_from_mapping()"]
    
    I --> S{"include_disulfide?"}
    S -->|Yes| T["Generate S-S<br/>constraints"]
    S -->|No| U["Skip S-S<br/>constraints"]
    
    T --> V["extract_sulfur_coordinates()"]
    V --> W["compute_distance_pairs()<br/>(S-S filter)"]
    W --> X["Check CYS residues<br/>in query"]
    X --> Y["generate_disulfide_constraints_internal()"]
    
    Y --> Z{"constraint_type?"}
    Z -->|nmr_distance| AA["Create NMR<br/>distance constraint"]
    Z -->|bond| AB["Create bond<br/>constraint"]
    Z -->|both| AC["Create both<br/>constraints"]
    
    R --> AD["Add to<br/>constraints list"]
    AA --> AD
    AB --> AD
    AC --> AD
    
    AD --> AE["Combine with<br/>explicit constraints"]
    G --> AE
    O --> AE
    U --> AE
    
    AE --> AF["Parse all<br/>constraints"]
    AF --> AG["Bond constraints<br/>→ connections"]
    AF --> AH["Min distance<br/>constraints"]
    AF --> AI["NMR distance<br/>constraints"]
    
    AG --> AJ["Add to Structure<br/>object"]
    AH --> AJ
    AI --> AJ
    
    AJ --> AK["Create Target<br/>object"]
    AK --> AL["Return to<br/>model"]
```

## 상세 처리 과정

### 1. YAML 파일 파싱
- `parse_boltz_schema()` 함수가 YAML 파일을 읽고 파싱
- `sequences` 섹션에서 protein 정보와 template 정보 추출

### 2. Template 정보 수집
```yaml
sequences:
  - protein:
      id: ["A"]
      sequence: "GCTESTCYSANOTHERCTESTTCG"
      template:
        structure: "8esv_A_tmpl.cif"
        chain_id: "A"
```

### 3. Template Constraint 생성
- 각 chain별로 template 정보가 있으면 constraint 생성
- `TemplateConstraintGenerator` 인스턴스 생성 및 활용

### 4. Sequence Alignment
- Needleman-Wunsch 알고리즘으로 template과 query sequence 정렬
- Sequence identity 계산 및 threshold 확인

### 5. Constraint 생성
#### Cb-Cb Distance Constraints
- Template 구조에서 Cb 좌표 추출
- 거리 필터링 (cb_distance_cutoff 이내)
- Query sequence에 mapping하여 constraint 생성

#### S-S Disulfide Constraints  
- Template 구조에서 CYS residue의 SG 좌표 추출
- 2.2Å 이내의 S-S bond 탐지
- Query sequence의 해당 위치가 CYS인지 확인
- NMR distance 또는 bond constraint로 생성

### 6. Constraint 통합
- Template에서 생성된 constraint와 YAML에 명시된 explicit constraint 병합
- 모든 constraint를 Structure 객체의 해당 필드에 저장

### 7. Model 적용
- **Bond constraints**: Tokenization 단계에서 처리 (강한 화학결합)
- **NMR distance constraints**: FK-steering에서 처리 (거리 범위 제약)

## 주요 파라미터

### TemplateConstraintGenerator 초기화 파라미터
- `distance_threshold`: 20.0 (최대 거리 제약)
- `cb_distance_cutoff`: 50.0 (Cb-Cb 최대 거리)
- `min_sequence_identity`: 0.6 (최소 sequence identity)
- `gap_penalty`: -2.0 (alignment gap penalty)
- `disulfide_distance_threshold`: 2.2 (S-S bond 임계값)
- `include_disulfide`: True (S-S constraint 포함 여부)

### Constraint 생성 파라미터
- `constraint_type`: "nmr_distance", "bond", 또는 "both"
- `distance_buffer`: NMR constraint의 거리 여유값
- `base_weight`: Constraint 가중치
- `sequence_identity_weight`: Sequence identity 기반 가중치 적용 여부

## 출력 형식

### NMR Distance Constraint
```python
{
    "nmr_distance": {
        "atom1": ["A", 10, "CB"],
        "atom2": ["A", 25, "CB"],
        "lower_bound": 3.5,
        "upper_bound": 4.5,
        "weight": 0.85
    }
}
```

### Bond Constraint
```python
{
    "bond": {
        "atom1": ["A", 10, "SG"],
        "atom2": ["A", 25, "SG"]
    }
}
```

## 구현 특징

1. **모듈화된 설계**: 각 기능이 독립적인 메서드로 분리되어 재사용성 향상
2. **일반화된 로직**: `_compute_distance_pairs()` 등 공통 로직을 일반화
3. **유연한 constraint 타입**: NMR distance와 bond constraint 모두 지원
4. **에러 처리**: 각 단계에서 실패 시 warning 출력 후 빈 리스트 반환
5. **성능 최적화**: Distance threshold로 불필요한 계산 최소화

## 사용 예시

```python
# Template constraint generator 초기화
generator = TemplateConstraintGenerator(
    include_disulfide=True,
    disulfide_distance_threshold=2.2
)

# Template constraint 생성
constraints = generator.generate_template_constraints(
    query_sequence="GCTESTCYSANOTHERCTESTTCG",
    template_structure="8esv_A_tmpl.cif",
    template_chain_id="A",
    query_chain_id="A",
    include_disulfide_nmr=True,
    include_disulfide_bond=True
) 
# Boltz Potentials System: 구조 예측을 위한 물리적 제약 조건 시스템

## 📋 개요 (Overview)

Boltz의 Potentials 시스템은 분자 구조 예측 과정에서 물리적 및 화학적 제약 조건을 강제하기 위한 핵심 메커니즘입니다. 이 시스템은 **확산 모델(Diffusion Model)**의 샘플링 과정에서 **guidance**와 **steering**을 통해 화학적으로 합리적인 구조를 생성하도록 돕습니다.

### 🎯 목적 (Purpose)
- **화학적 타당성 보장**: Bond length, angle, torsion 등 기본 화학 제약 준수
- **입체화학 유지**: Chiral center와 stereobond의 정확한 배치
- **물리적 충돌 방지**: <PERSON> 반발력과 steric clash 방지
- **사용자 정의 제약**: NMR distance constraint 등 실험 데이터 활용

---

## 🧠 이론적 배경 (Theoretical Background)

### 1. Guided Diffusion과 Potential-based Steering

#### 1.1 확산 모델에서의 Guidance
확산 모델은 노이즈에서 시작하여 점진적으로 데이터 분포로 수렴하는 과정입니다:

```
x_t = √(ᾱ_t) x_0 + √(1-ᾱ_t) ε
```

여기서 Boltz의 potentials는 **energy-based guidance**를 제공하여 샘플링 방향을 조정합니다.

#### 1.2 Energy-based Steering
각 potential은 에너지 함수 E(x)를 정의하고, 그래디언트 ∇E(x)를 통해 구조를 더 낮은 에너지 상태로 유도합니다:

```python
# Energy calculation
energy = potential.compute(coords, feats, parameters)

# Gradient-based update
gradient = potential.compute_gradient(coords, feats, parameters)
coords_updated = coords - learning_rate * gradient
```

### 2. Flat-bottom Potential Theory

대부분의 Boltz potentials는 **flat-bottom potential** 형태를 사용합니다:

```
E(r) = {
    0,                    if lower_bound ≤ r ≤ upper_bound
    k(r - upper_bound)²,  if r > upper_bound
    k(lower_bound - r)²,  if r < lower_bound
}
```

**물리적 의미**:
- **Flat region**: 허용 가능한 범위에서는 에너지 페널티 없음
- **Harmonic penalty**: 제약 위반 시 quadratic penalty로 강제 복원
- **Soft constraint**: Hard constraint와 달리 완전한 강제가 아닌 유연한 제약

---

## 🏗️ 코드 구조 분석 (Code Structure Analysis)

### 1. 기본 클래스 구조

#### 1.1 Abstract Base Class: `Potential`

```python
class Potential(ABC):
    def __init__(self, parameters=None, debug_enabled=False):
        """
        Initialize Potential with optional logging capabilities.
        
        Parameters
        ----------
        parameters : dict, optional
            Potential parameters including schedules and constants
        debug_enabled : bool, default=False
            Enable detailed debug logging for development
        """
        self.parameters = parameters
        self.debug_enabled = debug_enabled
        self.call_count = 0
        self.gradient_call_count = 0
```

**핵심 메서드들**:
- `compute()`: 에너지 계산 및 통계 추적
- `compute_gradient()`: 그래디언트 계산 및 역전파
- `compute_parameters()`: 시간에 따른 파라미터 스케줄링
- `compute_args()`: 구체적인 제약 조건 설정 (abstract)

#### 1.2 Core Computation Method

```python
def compute(self, coords, feats, parameters):
    """
    Main energy computation with comprehensive logging and COM handling.
    
    Process Flow:
    1. Extract constraint indices and parameters → compute_args()
    2. Handle center-of-mass transformations if needed
    3. Compute constraint variables (distances, angles, etc.) → compute_variable()
    4. Apply potential function to get energy → compute_function()
    5. Log statistics and activity detection
    """
    self.call_count += 1
    
    # Step 1: Extract constraint information
    index, args, com_args = self.compute_args(feats, parameters)
    
    # Early return if no constraints exist
    if index.shape[1] == 0:
        return torch.zeros(coords.shape[:-2], device=coords.device)
    
    # Step 2: Center-of-mass handling for chain-level constraints
    if com_args is not None:
        com_index, atom_pad_mask = com_args
        unpad_com_index = com_index[atom_pad_mask]
        unpad_coords = coords[..., atom_pad_mask, :]
        coords = torch.zeros(
            (*unpad_coords.shape[:-2], unpad_com_index.max() + 1, 3),
            device=coords.device,
        ).scatter_reduce(-2, unpad_com_index.unsqueeze(-1).expand_as(unpad_coords), unpad_coords, "mean")
    
    # Step 3-4: Core computation pipeline
    value = self.compute_variable(coords, index, compute_gradient=False)
    energy = self.compute_function(value, *args)
    result = energy.sum(dim=-1)
    
    # Step 5: Activity monitoring and statistics
    if self.debug_enabled and result is not None:
        total_energy = result.sum().item()
        if total_energy > 1e-6 and not hasattr(self, '_first_nonzero_logged'):
            self._log_debug(f"🎯 FIRST ACTIVITY: {self._potential_name} now contributing energy!")
            self._first_nonzero_logged = True
    
    return result
```

**코드 라인별 분석**:
- **Line 7-8**: 제약 조건 정보 추출 - 각 potential의 구체적 구현에 위임
- **Line 11-12**: 빠른 종료 조건 - 제약이 없으면 계산 비용 절약
- **Line 15-22**: COM 변환 - 체인 간 제약을 위해 원자 좌표를 질량중심 좌표로 변환
- **Line 25-27**: 핵심 계산 파이프라인 - 변수 계산 → 에너지 함수 적용 → 합산
- **Line 30-34**: 활동 감지 - 처음으로 에너지가 발생하는 순간 로깅

### 2. 특화된 Variable Computation

#### 2.1 Distance-based Variables

```python
class DistancePotential(Potential):
    def compute_variable(self, coords, index, compute_gradient=False):
        """
        Compute pairwise distances and gradients.
        
        Physics: r_ij = ||coords[i] - coords[j]||
        """
        r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
        r_ij_norm = torch.linalg.norm(r_ij, dim=-1)
        
        if not compute_gradient:
            return r_ij_norm
        
        # Gradient computation: ∇r_ij = r_ij / ||r_ij||
        r_hat_ij = r_ij / r_ij_norm.unsqueeze(-1)
        grad_i = r_hat_ij       # ∂r/∂coords[i] = +û_ij
        grad_j = -1 * r_hat_ij  # ∂r/∂coords[j] = -û_ij
        grad = torch.stack((grad_i, grad_j), dim=1)
        
        return r_ij_norm, grad
```

**물리적 의미**:
- **거리 계산**: 유클리드 공간에서 두 점 간의 직선 거리
- **그래디언트**: 거리 변화율의 방향성 - 단위벡터로 표현
- **대칭성**: 원자 i와 j에 대해 크기는 같고 방향은 반대

---

## 🔧 주요 Potential 클래스들 (Key Potential Classes)

### 1. `PoseBustersPotential`: 기본 화학 제약

```python
class PoseBustersPotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        """
        RDKit-derived chemical constraints for bonds, angles, and clashes.
        
        Chemical basis:
        - Bond constraints: 공유결합 길이 (예: C-C ~1.54Å, C=C ~1.34Å)
        - Angle constraints: 결합각 범위 (예: sp³ ~109.5°, sp² ~120°)
        - Clash constraints: 비결합 원자 간 최소 거리
        """
        pair_index = feats["rdkit_bounds_index"][0]
        lower_bounds = feats["rdkit_lower_bounds"][0].clone()
        upper_bounds = feats["rdkit_upper_bounds"][0].clone()
        bond_mask = feats["rdkit_bounds_bond_mask"][0]
        angle_mask = feats["rdkit_bounds_angle_mask"][0]
        
        # Apply type-specific buffers
        lower_bounds[bond_mask * ~angle_mask] *= 1.0 - parameters["bond_buffer"]      # Bond: ±20%
        upper_bounds[bond_mask * ~angle_mask] *= 1.0 + parameters["bond_buffer"]
        lower_bounds[~bond_mask * angle_mask] *= 1.0 - parameters["angle_buffer"]     # Angle: ±20%
        upper_bounds[~bond_mask * angle_mask] *= 1.0 + parameters["angle_buffer"]
        lower_bounds[~bond_mask * ~angle_mask] *= 1.0 - parameters["clash_buffer"]    # Clash: -15%
        upper_bounds[~bond_mask * ~angle_mask] = float("inf")                         # No upper limit
        
        k = torch.ones_like(lower_bounds)
        return pair_index, (k, lower_bounds, upper_bounds), None
```

### 2. `NMRDistancePotential`: 실험 데이터 기반 제약

```python
class NMRDistancePotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        """
        Experimental distance constraints from NMR NOE data.
        
        Experimental basis:
        - NOE intensity ∝ 1/r⁶ (Nuclear Overhauser Effect)
        - Strong NOE: 2-3Å, Medium: 3-4Å, Weak: 4-5Å
        """
        pair_index = feats["nmr_distance_atom_index"][0]
        lower_bounds = feats["nmr_distance_lower_bounds"][0].clone()
        upper_bounds = feats["nmr_distance_upper_bounds"][0].clone()
        weights = feats["nmr_distance_weights"][0]
        
        # Apply experimental uncertainty buffers
        lower_bounds = lower_bounds * (1.0 - parameters["lower_buffer"])  # 10% tolerance
        finite_mask = torch.isfinite(upper_bounds)
        upper_bounds[finite_mask] = upper_bounds[finite_mask] * (1.0 + parameters["upper_buffer"])
        
        # Convert weights to force constants
        k = weights * parameters["base_force_constant"]
        
        return pair_index, (k, lower_bounds, upper_bounds), None
```

### 3. `ChiralAtomPotential`: 입체화학 보존

```python
class ChiralAtomPotential(FlatBottomPotential, DihedralPotential):
    def compute_args(self, feats, parameters):
        """
        Maintain R/S configuration of chiral centers.
        
        Stereochemistry:
        - R (rectus): 우회전 배치, 양의 이면각
        - S (sinister): 좌회전 배치, 음의 이면각
        """
        chiral_atom_index = feats["chiral_atom_index"][0]
        chiral_atom_orientations = feats["chiral_atom_orientations"][0].bool()
        
        lower_bounds = torch.zeros(chiral_atom_orientations.shape, device=device)
        upper_bounds = torch.zeros(chiral_atom_orientations.shape, device=device)
        
        # R configuration: φ > buffer (positive dihedral)
        lower_bounds[chiral_atom_orientations] = parameters["buffer"]
        upper_bounds[chiral_atom_orientations] = float("inf")
        
        # S configuration: φ < -buffer (negative dihedral)
        upper_bounds[~chiral_atom_orientations] = -1 * parameters["buffer"]
        lower_bounds[~chiral_atom_orientations] = float("-inf")
        
        k = torch.ones_like(lower_bounds)
        return chiral_atom_index, (k, lower_bounds, upper_bounds), None
```

---

## 🎮 Diffusion Model에서의 활용 (Integration with Diffusion)

### 1. Sampling Loop의 핵심 로직

```python
def sample(self, atom_mask, steering_args=None, **network_condition_kwargs):
    """
    Diffusion sampling with potential-based guidance and resampling.
    
    Main components:
    1. FK (Filter-Kernel) Resampling: Energy-based particle filtering
    2. Gradient Guidance: Direct energy minimization
    """
    potentials = get_potentials()  # Load all potential functions
    
    # Initialize for FK resampling
    if steering_args["fk_steering"]:
        multiplicity = multiplicity * steering_args["num_particles"]
        energy_traj = torch.empty((multiplicity, 0), device=self.device)
    
    # Main denoising loop
    for step_idx, (sigma_t, sigma_next, gamma_next) in enumerate(sigmas_and_gammas):
        
        # 1. Network prediction: noise → structure
        pred_eps, token_repr = self.preconditioned_network_forward(
            atom_coords, sigma_t, network_condition_kwargs, training=False
        )
        atom_coords_denoised = self.c_skip(sigma_t) * atom_coords + self.c_out(sigma_t) * pred_eps
        
        # 2. FK Resampling: filter out high-energy structures
        if steering_args["fk_steering"] and (step_idx % steering_args["fk_resampling_interval"] == 0):
            energy = torch.zeros(multiplicity, device=self.device)
            for potential in potentials:
                parameters = potential.compute_parameters(steering_t)
                if parameters["resampling_weight"] > 0:
                    component_energy = potential.compute(atom_coords_denoised, feats, parameters)
                    energy += parameters["resampling_weight"] * component_energy
            
            # Boltzmann-like resampling weights
            log_G = energy_traj[:, -2] - energy_traj[:, -1] if step_idx > 0 else -energy
            resample_weights = F.softmax(
                (ll_difference + steering_args["fk_lambda"] * log_G).reshape(-1, steering_args["num_particles"]),
                dim=1
            )
        
        # 3. Gradient Guidance: optimize structure directly
        if steering_args["guidance_update"] and step_idx < num_sampling_steps - 1:
            guidance_update = torch.zeros_like(atom_coords_denoised)
            for guidance_step in range(steering_args["num_gd_steps"]):
                energy_gradient = torch.zeros_like(atom_coords_denoised)
                for potential in potentials:
                    parameters = potential.compute_parameters(steering_t)
                    if (parameters["guidance_weight"] > 0 and 
                        guidance_step % parameters["guidance_interval"] == 0):
                        energy_gradient += parameters["guidance_weight"] * potential.compute_gradient(
                            atom_coords_denoised + guidance_update, feats, parameters
                        )
                guidance_update -= energy_gradient  # Gradient descent: x ← x - α∇E(x)
            
            atom_coords_denoised += guidance_update
```

### 2. 핵심 메커니즘 분석

#### 2.1 FK Resampling의 물리적 의미

```python
# Energy-based particle filtering
log_G = energy_previous - energy_current  # Energy improvement = "fitness"
resample_weights = softmax(fk_lambda * log_G)  # Boltzmann factor exp(-βE)
```

**물리적 해석**:
- **Boltzmann distribution**: 낮은 에너지 상태가 높은 확률을 가짐
- **Particle filtering**: 몬테카를로 방법의 importance sampling
- **Energy landscape navigation**: 에너지 최소값 주변으로 샘플 집중

#### 2.2 Gradient Guidance의 수학적 기반

```python
# Multi-step gradient descent in energy landscape
for guidance_step in range(num_gd_steps):
    grad_E = sum(weight * potential.compute_gradient(x) for potential in potentials)
    x = x - learning_rate * grad_E  # Steepest descent
```

**수학적 의미**:
- **에너지 최소화**: ∇E(x) = 0인 지점으로 수렴
- **다목적 최적화**: 여러 제약 조건의 가중합 최소화
- **Local refinement**: 각 denoising step에서 지역적 구조 개선

---

## 📊 Parameter Configuration & Scheduling

### 1. Potential 설정 예시

```python
def get_potentials(debug_enabled=True):
    """Configure all potentials with their scheduling parameters."""
    potentials = [
        # High-frequency, critical constraints
        NMRDistancePotential(
            parameters={
                "guidance_interval": 1,                    # Apply every step
                "guidance_weight": ExponentialInterpolation(
                    start=0.05, end=0.2, alpha=-2.0      # Increase over time
                ),
                "resampling_weight": 1.0,                  # Full strength for resampling
                "lower_buffer": 0.1,                       # 10% tolerance
                "upper_buffer": 0.1,
                "base_force_constant": 1.0,
            }
        ),
        
        # Moderate frequency, chemical validity
        PoseBustersPotential(
            parameters={
                "guidance_interval": 1,                    # Apply every step
                "guidance_weight": 1.0,                    # Strong enforcement
                "resampling_weight": 0.1,                  # Light resampling
                "bond_buffer": 0.20,                       # 20% bond tolerance
                "angle_buffer": 0.20,
                "clash_buffer": 0.15,                      # 15% clash tolerance
            }
        ),
        
        # Low frequency, expensive computation
        VDWOverlapPotential(
            parameters={
                "guidance_interval": 5,                    # Every 5 steps
                "guidance_weight": PiecewiseStepFunction(
                    thresholds=[0.4], values=[0.125, 0.0] # Disable after t=0.4
                ),
                "resampling_weight": PiecewiseStepFunction(
                    thresholds=[0.6], values=[0.01, 0.0]  # Disable after t=0.6
                ),
                "buffer": 0.225,                           # 22.5% VDW tolerance
            }
        ),
    ]
    return potentials
```

### 2. 스케줄링 전략의 의미

**Frequency-based scheduling**:
- **Critical constraints**: 매 step 적용으로 일관된 품질 보장
- **Expensive constraints**: 간헐적 적용으로 계산 효율성 확보
- **Context-dependent**: 초기에는 거시적, 후기에는 미시적 제약 강조

**Weight-based scheduling**:
- **Progressive enforcement**: 시간에 따라 제약 강도 증가
- **Early flexibility**: 초기에는 탐색 공간 확보
- **Late precision**: 후기에는 정밀한 구조 refinement

---

## 🎯 결론 및 핵심 기여도 (Conclusion)

### 1. 시스템의 핵심 가치

Boltz의 Potentials 시스템은 다음과 같은 **고유한 가치**를 제공합니다:

**🔬 과학적 기여**:
- **Multi-scale physics**: 원자 수준부터 분자 수준까지 다층적 제약
- **Experimental integration**: NMR, X-ray 등 실험 데이터의 직접적 활용
- **Chemical validity**: 기본 화학 법칙의 체계적 강제

**💻 기술적 혁신**:
- **Soft constraints**: Hard constraint의 수치적 불안정성 해결
- **Adaptive scheduling**: 상황별 최적화로 성능과 품질의 균형
- **Modular design**: 새로운 제약 조건의 쉬운 확장성

**🚀 실용적 효과**:
- **Prediction quality**: 화학적으로 타당한 구조 생성률 향상
- **Computational efficiency**: 스마트 스케줄링으로 비용 최적화
- **User control**: 실험 데이터 기반 사용자 정의 제약 지원

### 2. 물리적 의미와 수학적 엄밀성

**에너지 기반 접근법**:
```
E_total = ∑ᵢ wᵢ E_potential_i(x)
x* = argmin E_total(x)  subject to  structural_constraints
```

이는 자연계의 **에너지 최소화 원리**를 충실히 구현하여:
- **Thermodynamic consistency**: 볼츠만 분포를 따르는 realistic sampling
- **Physical realism**: 실제 분자의 에너지 landscape 근사
- **Mathematical rigor**: 미분 가능한 목적함수로 gradient-based 최적화 지원

### 3. 미래 발전 가능성

**확장 방향**:
- **Advanced constraints**: Secondary structure, loop regions, domain interfaces
- **Machine learning integration**: Neural potential learning from experimental data
- **Multi-resolution**: Coarse-grained → all-atom progressive refinement

이러한 포괄적 접근을 통해 Boltz는 단순한 생성 모델을 넘어서 **물리적으로 의미 있고 화학적으로 타당한** 분자 구조 예측 도구로 자리매김하고 있습니다. 
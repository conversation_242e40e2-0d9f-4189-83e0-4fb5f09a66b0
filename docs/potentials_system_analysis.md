# Boltz Potentials System: 구조 예측을 위한 물리적 제약 조건 시스템

## 📋 개요 (Overview)

Boltz의 Potentials 시스템은 분자 구조 예측 과정에서 물리적 및 화학적 제약 조건을 강제하기 위한 핵심 메커니즘입니다. 이 시스템은 **확산 모델(Diffusion Model)**의 샘플링 과정에서 **guidance**와 **steering**을 통해 화학적으로 합리적인 구조를 생성하도록 돕습니다.

### 🎯 목적 (Purpose)
- **화학적 타당성 보장**: Bond length, angle, torsion 등 기본 화학 제약 준수
- **입체화학 유지**: Chiral center와 stereobond의 정확한 배치
- **물리적 충돌 방지**: <PERSON> 반발력과 steric clash 방지
- **사용자 정의 제약**: NMR distance constraint 등 실험 데이터 활용

---

## 🧠 이론적 배경 (Theoretical Background)

### 1. Guided Diffusion과 Potential-based Steering

#### 1.1 확산 모델에서의 Guidance
확산 모델은 노이즈에서 시작하여 점진적으로 데이터 분포로 수렴하는 과정입니다:

```
x_t = √(ᾱ_t) x_0 + √(1-ᾱ_t) ε
```

여기서 Boltz의 potentials는 **energy-based guidance**를 제공하여 샘플링 방향을 조정합니다.

#### 1.2 Energy-based Steering
각 potential은 에너지 함수 E(x)를 정의하고, 그래디언트 ∇E(x)를 통해 구조를 더 낮은 에너지 상태로 유도합니다:

```python
# Energy calculation
energy = potential.compute(coords, feats, parameters)

# Gradient-based update
gradient = potential.compute_gradient(coords, feats, parameters)
coords_updated = coords - learning_rate * gradient
```

### 2. Flat-bottom Potential Theory

대부분의 Boltz potentials는 **flat-bottom potential** 형태를 사용합니다:

```
E(r) = {
    0,                    if lower_bound ≤ r ≤ upper_bound
    k(r - upper_bound)²,  if r > upper_bound
    k(lower_bound - r)²,  if r < lower_bound
}
```

**물리적 의미**:
- **Flat region**: 허용 가능한 범위에서는 에너지 페널티 없음
- **Harmonic penalty**: 제약 위반 시 quadratic penalty로 강제 복원
- **Soft constraint**: Hard constraint와 달리 완전한 강제가 아닌 유연한 제약

---

## 🏗️ 코드 구조 분석 (Code Structure Analysis)

### 1. 기본 클래스 구조

#### 1.1 Abstract Base Class: `Potential`

```python
class Potential(ABC):
    def __init__(self, parameters=None, debug_enabled=False):
        """
        Initialize Potential with optional logging capabilities.
        
        Parameters
        ----------
        parameters : dict, optional
            Potential parameters including schedules and constants
        debug_enabled : bool, default=False
            Enable detailed debug logging for development
        """
        self.parameters = parameters
        self.debug_enabled = debug_enabled
        self.call_count = 0
        self.gradient_call_count = 0
```

**핵심 메서드들**:
- `compute()`: 에너지 계산 및 통계 추적
- `compute_gradient()`: 그래디언트 계산 및 역전파
- `compute_parameters()`: 시간에 따른 파라미터 스케줄링
- `compute_args()`: 구체적인 제약 조건 설정 (abstract)

#### 1.2 Core Computation Methods

```python
def compute(self, coords, feats, parameters):
    """
    Main energy computation method with comprehensive logging.
    
    Process:
    1. Extract constraint indices and parameters
    2. Handle center-of-mass (COM) transformations if needed
    3. Compute constraint variables (distances, angles, etc.)
    4. Apply potential function to get energy
    5. Log statistics and first activity detection
    """
    self.call_count += 1
    
    # Extract constraint information
    index, args, com_args = self.compute_args(feats, parameters)
    
    # Early return if no constraints
    if index.shape[1] == 0:
        return torch.zeros(coords.shape[:-2], device=coords.device)
    
    # Center-of-mass handling for chain-level constraints
    if com_args is not None:
        com_index, atom_pad_mask = com_args
        # Transform atom coordinates to COM coordinates
        unpad_com_index = com_index[atom_pad_mask]
        unpad_coords = coords[..., atom_pad_mask, :]
        coords = torch.zeros(
            (*unpad_coords.shape[:-2], unpad_com_index.max() + 1, 3),
            device=coords.device,
        ).scatter_reduce(
            -2,
            unpad_com_index.unsqueeze(-1).expand_as(unpad_coords),
            unpad_coords,
            "mean",
        )
    
    # Core computation
    value = self.compute_variable(coords, index, compute_gradient=False)
    energy = self.compute_function(value, *args)
    result = energy.sum(dim=-1)
    
    # Statistical logging and activity detection
    if self.debug_enabled and result is not None:
        total_energy = result.sum().item()
        if total_energy > 1e-6:
            self._log_debug(f"Total energy: {total_energy:.2f}")
    
    return result
```

**코드 의미 분석**:
1. **Call counting**: 디버깅과 성능 분석을 위한 호출 횟수 추적
2. **COM transformation**: 체인 단위 제약 조건을 위한 좌표 변환
3. **Early termination**: 제약 조건이 없는 경우 빠른 종료
4. **Energy aggregation**: 개별 제약의 에너지를 합산하여 총 페널티 계산

### 2. 특화된 Potential 클래스들

#### 2.1 Distance-based Potentials

```python
class DistancePotential(Potential):
    def compute_variable(self, coords, index, compute_gradient=False):
        """
        Compute pairwise distances and their gradients.
        
        Physics:
        - r_ij = ||coords[i] - coords[j]||
        - ∇r_ij = (coords[i] - coords[j]) / r_ij  (unit vector)
        """
        r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
        r_ij_norm = torch.linalg.norm(r_ij, dim=-1)
        
        if not compute_gradient:
            return r_ij_norm
        
        r_hat_ij = r_ij / r_ij_norm.unsqueeze(-1)
        grad_i = r_hat_ij      # ∂r/∂coords[i]
        grad_j = -1 * r_hat_ij # ∂r/∂coords[j]
        grad = torch.stack((grad_i, grad_j), dim=1)
        
        return r_ij_norm, grad
```

**물리적 의미**:
- **Distance computation**: 두 원자 간 유클리드 거리 계산
- **Gradient**: 거리 변화에 대한 각 원자의 기여도 (방향성)
- **Unit vector**: 정규화된 방향 벡터로 힘의 방향 결정

#### 2.2 Dihedral Angle Potentials

```python
class DihedralPotential(Potential):
    def compute_variable(self, coords, index, compute_gradient=False):
        """
        Compute dihedral angles using cross products.
        
        Physics:
        - 4개 원자 (i-j-k-l)로 정의되는 비틀림각
        - 두 평면 (ijk, jkl) 사이의 각도
        - φ = arccos(n₁·n₂ / |n₁||n₂|)
        """
        r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
        r_kj = coords.index_select(-2, index[2]) - coords.index_select(-2, index[1])
        r_kl = coords.index_select(-2, index[2]) - coords.index_select(-2, index[3])
        
        # Normal vectors to planes
        n_ijk = torch.cross(r_ij, r_kj, dim=-1)
        n_jkl = torch.cross(r_kj, r_kl, dim=-1)
        
        # Angle computation with sign determination
        r_kj_norm = torch.linalg.norm(r_kj, dim=-1)
        n_ijk_norm = torch.linalg.norm(n_ijk, dim=-1)
        n_jkl_norm = torch.linalg.norm(n_jkl, dim=-1)
        
        sign_phi = torch.sign(
            r_kj.unsqueeze(-2) @ torch.cross(n_ijk, n_jkl, dim=-1).unsqueeze(-1)
        ).squeeze(-1, -2)
        
        phi = sign_phi * torch.arccos(
            torch.clamp(
                (n_ijk.unsqueeze(-2) @ n_jkl.unsqueeze(-1)).squeeze(-1, -2)
                / (n_ijk_norm * n_jkl_norm),
                -1 + 1e-8, 1 - 1e-8,
            )
        )
        
        return phi
```

**물리적 의미**:
- **Dihedral angle**: 분자의 conformational flexibility를 결정하는 핵심 각도
- **Cross product**: 두 벡터에 수직인 법선 벡터로 평면 정의
- **Sign determination**: 회전 방향(시계/반시계)을 구분하는 부호 결정
- **Numerical stability**: arccos 함수의 정의역 제한으로 수치적 안정성 확보

---

## 🔧 구체적인 Potential 클래스들 (Specific Potential Classes)

### 1. `PoseBustersPotential`: 기본 화학 제약

```python
class PoseBustersPotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        """
        RDKit에서 계산된 bond length와 angle constraint 적용.
        
        Chemical constraints:
        - Bond constraints: 공유결합 길이 제한
        - Angle constraints: 결합각 제한  
        - Clash constraints: 비결합 원자 간 충돌 방지
        """
        pair_index = feats["rdkit_bounds_index"][0]
        lower_bounds = feats["rdkit_lower_bounds"][0].clone()
        upper_bounds = feats["rdkit_upper_bounds"][0].clone()
        bond_mask = feats["rdkit_bounds_bond_mask"][0]
        angle_mask = feats["rdkit_bounds_angle_mask"][0]
        
        # Apply buffer parameters for different constraint types
        lower_bounds[bond_mask * ~angle_mask] *= 1.0 - parameters["bond_buffer"]
        upper_bounds[bond_mask * ~angle_mask] *= 1.0 + parameters["bond_buffer"]
        lower_bounds[~bond_mask * angle_mask] *= 1.0 - parameters["angle_buffer"]
        upper_bounds[~bond_mask * angle_mask] *= 1.0 + parameters["angle_buffer"]
        
        # Clash prevention: only lower bound with buffer
        lower_bounds[~bond_mask * ~angle_mask] *= 1.0 - parameters["clash_buffer"]
        upper_bounds[~bond_mask * ~angle_mask] = float("inf")
        
        k = torch.ones_like(lower_bounds)
        return pair_index, (k, lower_bounds, upper_bounds), None
```

**화학적 의미**:
- **Bond constraints**: 공유결합의 정상 범위 유지 (예: C-C 결합 ~1.54Å)
- **Angle constraints**: 결합각의 허용 범위 설정 (예: sp³ 탄소 ~109.5°)
- **Clash prevention**: 비결합 원자들의 van der Waals 반발 방지
- **Buffer system**: 각 제약 유형별 유연성 조절

### 2. `NMRDistancePotential`: 실험 데이터 기반 제약

```python
class NMRDistancePotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        """
        NMR NOE 데이터로부터 유도된 거리 제약 조건.
        
        Experimental basis:
        - NOE intensity ∝ 1/r⁶ (Nuclear Overhauser Effect)
        - Strong NOE: ~2-3Å 거리
        - Medium NOE: ~3-4Å 거리
        - Weak NOE: ~4-5Å 거리
        """
        pair_index = feats["nmr_distance_atom_index"][0]
        lower_bounds = feats["nmr_distance_lower_bounds"][0].clone()
        upper_bounds = feats["nmr_distance_upper_bounds"][0].clone()
        weights = feats["nmr_distance_weights"][0]
        
        # Apply buffers for experimental uncertainty
        lower_bounds = lower_bounds * (1.0 - parameters["lower_buffer"])
        finite_mask = torch.isfinite(upper_bounds)
        upper_bounds[finite_mask] = upper_bounds[finite_mask] * (1.0 + parameters["upper_buffer"])
        
        # Weight-based force constants
        k = weights * parameters["base_force_constant"]
        
        return pair_index, (k, lower_bounds, upper_bounds), None
```

**실험적 기반**:
- **NOE 효과**: 핵 간 거리에 따른 스핀-스핀 상호작용
- **거리-강도 관계**: NOE 강도가 거리의 6제곱에 반비례
- **불확실성 처리**: 실험 오차를 고려한 buffer 적용
- **가중치 시스템**: 실험 데이터의 신뢰도 반영

### 3. `VDWOverlapPotential`: 물리적 충돌 방지

```python
class VDWOverlapPotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        """
        Van der Waals 반발력을 이용한 원자간 충돌 방지.
        
        Physical basis:
        - VDW radius: 각 원소별 고유한 van der Waals 반지름
        - Contact distance: r_vdw(A) + r_vdw(B)
        - Exclusion principle: 두 원자는 contact distance보다 가까워질 수 없음
        """
        # Get VDW radii from periodic table constants
        vdw_radii = torch.zeros(const.num_elements, dtype=torch.float32, device=device)
        vdw_radii[1:119] = torch.tensor(const.vdw_radii, dtype=torch.float32, device=device)
        atom_vdw_radii = (feats["ref_element"].float() @ vdw_radii.unsqueeze(-1)).squeeze(-1)[0]
        
        # Generate all pairwise combinations
        pair_index = torch.triu_indices(atom_chain_id.shape[0], atom_chain_id.shape[0], 1, device=device)
        
        # Filter: only non-connected, non-bonded pairs from different chains
        pair_pad_mask = atom_pad_mask[pair_index].all(dim=0)
        connected_chain_mask = connected_chain_matrix[
            atom_chain_id[pair_index[0]], atom_chain_id[pair_index[1]]
        ]
        pair_index = pair_index[:, pair_pad_mask * ~connected_chain_mask]
        
        # Lower bound = sum of VDW radii with buffer
        lower_bounds = atom_vdw_radii[pair_index].sum(dim=0) * (1.0 - parameters["buffer"])
        upper_bounds = None  # No upper limit for VDW repulsion
        
        return pair_index, (k, lower_bounds, upper_bounds), None
```

**물리적 기반**:
- **Pauli exclusion**: 두 원자의 전자 궤도는 겹칠 수 없음
- **VDW radius**: 각 원소의 고유한 크기 정의
- **Contact distance**: 두 원자가 접촉할 수 있는 최소 거리
- **Chain filtering**: 연결된 체인 간에는 적용하지 않음 (이미 다른 제약으로 처리)

### 4. Stereochemistry Potentials

#### 4.1 `ChiralAtomPotential`: 키랄 중심 보존

```python
class ChiralAtomPotential(FlatBottomPotential, DihedralPotential):
    def compute_args(self, feats, parameters):
        """
        Chiral center의 R/S 배치 유지.
        
        Stereochemistry:
        - Chiral center: 4개의 서로 다른 치환기를 가진 탄소
        - R/S designation: Cahn-Ingold-Prelog priority rules
        - Dihedral angle sign: 양수(R) 또는 음수(S) 배치
        """
        chiral_atom_index = feats["chiral_atom_index"][0]
        chiral_atom_orientations = feats["chiral_atom_orientations"][0].bool()
        
        lower_bounds = torch.zeros(chiral_atom_orientations.shape, device=device)
        upper_bounds = torch.zeros(chiral_atom_orientations.shape, device=device)
        
        # R configuration: positive dihedral angle
        lower_bounds[chiral_atom_orientations] = parameters["buffer"]
        upper_bounds[chiral_atom_orientations] = float("inf")
        
        # S configuration: negative dihedral angle
        upper_bounds[~chiral_atom_orientations] = -1 * parameters["buffer"]
        lower_bounds[~chiral_atom_orientations] = float("-inf")
        
        k = torch.ones_like(lower_bounds)
        return chiral_atom_index, (k, lower_bounds, upper_bounds), None
```

**입체화학적 의미**:
- **R configuration**: 우회전(clockwise) 배치, 양의 이면각
- **S configuration**: 좌회전(counter-clockwise) 배치, 음의 이면각
- **Buffer zone**: 완전한 강제가 아닌 유연한 제약으로 수치적 안정성 확보
- **Asymmetric bounds**: 한쪽 방향으로만 제약하여 올바른 키랄성 유지

#### 4.2 `StereoBondPotential`: E/Z 이성질체 제어

```python
class StereoBondPotential(FlatBottomPotential, AbsDihedralPotential):
    def compute_args(self, feats, parameters):
        """
        Double bond의 E/Z configuration 유지.
        
        Stereochemistry:
        - E (trans): 높은 우선순위 그룹이 반대편 (|φ| > 90°)
        - Z (cis): 높은 우선순위 그룹이 같은 편 (|φ| < 90°)
        - Absolute dihedral: |φ| 값으로 cis/trans 구분
        """
        stereo_bond_index = feats["stereo_bond_index"][0]
        stereo_bond_orientations = feats["stereo_bond_orientations"][0].bool()
        
        lower_bounds = torch.zeros(stereo_bond_orientations.shape, device=device)
        upper_bounds = torch.zeros(stereo_bond_orientations.shape, device=device)
        
        # E configuration: |φ| > π/2 (trans)
        lower_bounds[stereo_bond_orientations] = torch.pi - parameters["buffer"]
        upper_bounds[stereo_bond_orientations] = float("inf")
        
        # Z configuration: |φ| < π/2 (cis)
        lower_bounds[~stereo_bond_orientations] = float("-inf")
        upper_bounds[~stereo_bond_orientations] = parameters["buffer"]
        
        k = torch.ones_like(lower_bounds)
        return stereo_bond_index, (k, lower_bounds, upper_bounds), None
```

**기하학적 의미**:
- **E (trans)**: 치환기들이 이중결합 반대편에 위치, 더 안정한 형태
- **Z (cis)**: 치환기들이 이중결합 같은 편에 위치, 입체장애 가능
- **Absolute angle**: 절댓값 사용으로 회전 대칭성 고려
- **Buffer margin**: 완전한 평면 구조를 강요하지 않고 유연성 허용

---

## 🎮 Diffusion Model에서의 사용 (Usage in Diffusion Model)

### 1. Sampling Loop에서의 통합

```python
def sample(self, atom_mask, num_sampling_steps=None, multiplicity=1, 
           max_parallel_samples=None, steering_args=None, **network_condition_kwargs):
    """
    Diffusion sampling with potential-based steering.
    
    Process:
    1. Initialize with noise
    2. For each denoising step:
       a. Predict denoised structure
       b. Compute potential energies
       c. Apply guidance updates
       d. Perform resampling if needed
    3. Return final structure
    """
    potentials = get_potentials()  # Load all potential functions
    
    if steering_args["fk_steering"]:
        multiplicity = multiplicity * steering_args["num_particles"]
        energy_traj = torch.empty((multiplicity, 0), device=self.device)
    
    if steering_args["guidance_update"]:
        scaled_guidance_update = torch.zeros(
            (multiplicity, *atom_mask.shape[1:], 3),
            dtype=torch.float32, device=self.device
        )
    
    # Main sampling loop
    for step_idx, (sigma_t, sigma_next, gamma_next) in enumerate(sigmas_and_gammas):
        # 1. Network prediction
        pred_eps, token_repr = self.preconditioned_network_forward(
            atom_coords, sigma_t, network_condition_kwargs, training=False
        )
        
        # 2. Compute denoised prediction
        atom_coords_denoised = self.c_skip(sigma_t) * atom_coords + self.c_out(sigma_t) * pred_eps
        
        # 3. FK Resampling based on energy
        if steering_args["fk_steering"] and (step_idx % steering_args["fk_resampling_interval"] == 0):
            energy = torch.zeros(multiplicity, device=self.device)
            for potential in potentials:
                parameters = potential.compute_parameters(steering_t)
                if parameters["resampling_weight"] > 0:
                    component_energy = potential.compute(
                        atom_coords_denoised, network_condition_kwargs["feats"], parameters
                    )
                    energy += parameters["resampling_weight"] * component_energy
            
            # Compute resampling weights using energy differences
            log_G = energy_traj[:, -2] - energy_traj[:, -1] if step_idx > 0 else -energy
            resample_weights = F.softmax(
                (ll_difference + steering_args["fk_lambda"] * log_G).reshape(-1, steering_args["num_particles"]),
                dim=1
            )
        
        # 4. Guidance update through gradient descent
        if steering_args["guidance_update"] and step_idx < num_sampling_steps - 1:
            guidance_update = torch.zeros_like(atom_coords_denoised)
            for guidance_step in range(steering_args["num_gd_steps"]):
                energy_gradient = torch.zeros_like(atom_coords_denoised)
                for potential in potentials:
                    parameters = potential.compute_parameters(steering_t)
                    if (parameters["guidance_weight"] > 0 and 
                        guidance_step % parameters["guidance_interval"] == 0):
                        energy_gradient += parameters["guidance_weight"] * potential.compute_gradient(
                            atom_coords_denoised + guidance_update,
                            network_condition_kwargs["feats"], parameters
                        )
                guidance_update -= energy_gradient  # Gradient descent step
            
            atom_coords_denoised += guidance_update
        
        # 5. Next step preparation
        # ... (noise addition and coordinate update)
```

### 2. 핵심 메커니즘 분석

#### 2.1 FK (Filter-Kernel) Resampling

**목적**: 높은 에너지(위반)를 가진 샘플들을 낮은 에너지 샘플들로 교체

```python
# Energy-based particle filtering
energy = sum(weight * potential.compute(...) for potential in potentials)
log_G = energy_previous - energy_current  # Energy improvement
resample_weights = softmax(fk_lambda * log_G)  # Higher weight for better structures
```

**물리적 의미**:
- **Boltzmann factor**: exp(-βE) 형태의 에너지 기반 확률 분포
- **Particle filtering**: 몬테카를로 방법의 importance sampling
- **Energy landscape**: 낮은 에너지 영역으로 샘플 집중

#### 2.2 Gradient-based Guidance

**목적**: 각 denoising step에서 제약 조건을 더 잘 만족하는 방향으로 구조 수정

```python
# Multi-step gradient descent
for guidance_step in range(num_gd_steps):
    energy_gradient = torch.zeros_like(coords)
    for potential in potentials:
        if potential.is_active(guidance_step):
            energy_gradient += weight * potential.compute_gradient(coords, ...)
    coords -= learning_rate * energy_gradient  # Move toward lower energy
```

**수학적 기반**:
- **Gradient descent**: ∇E(x) 방향으로 에너지 최소화
- **Multi-objective**: 여러 제약 조건의 가중합으로 총 목적함수 구성
- **Step scheduling**: 각 potential별 적용 빈도 조절로 효율성 향상

### 3. Parameter Scheduling

```python
def get_potentials(debug_enabled=True):
    """
    Configure all potentials with their parameters.
    
    각 potential은 다음 파라미터들을 가집니다:
    - guidance_interval: guidance 적용 주기
    - guidance_weight: guidance 강도
    - resampling_weight: resampling 시 가중치
    - buffer: 제약 조건의 허용 오차
    """
    potentials = [
        SymmetricChainCOMPotential(
            parameters={
                "guidance_interval": 4,           # 4 step마다 적용
                "guidance_weight": 0.5,           # 중간 강도
                "resampling_weight": 0.5,         # 중간 강도
                "buffer": ExponentialInterpolation(start=1.0, end=5.0, alpha=-2.0),
            }
        ),
        VDWOverlapPotential(
            parameters={
                "guidance_interval": 5,           # 5 step마다 적용 (비용 고려)
                "guidance_weight": PiecewiseStepFunction(
                    thresholds=[0.4], values=[0.125, 0.0]  # t>0.4에서 비활성화
                ),
                "resampling_weight": PiecewiseStepFunction(
                    thresholds=[0.6], values=[0.01, 0.0]   # t>0.6에서 비활성화
                ),
                "buffer": 0.225,                  # 22.5% VDW radius 완화
            }
        ),
        NMRDistancePotential(
            parameters={
                "guidance_interval": 1,           # 매 step 적용 (중요한 제약)
                "guidance_weight": ExponentialInterpolation(
                    start=0.05, end=0.2, alpha=-2.0  # 시간에 따라 증가
                ),
                "resampling_weight": 1.0,         # 최대 강도
                "lower_buffer": 0.1,              # 10% 하한 완화
                "upper_buffer": 0.1,              # 10% 상한 완화
                "base_force_constant": 1.0,       # 기본 강도
            }
        ),
        # ... 기타 potentials
    ]
    return potentials
```

**스케줄링 전략**:
- **Frequency control**: 중요한 제약은 매 step, 비용이 큰 제약은 간헐적 적용
- **Weight scheduling**: 시간에 따라 가중치 조절 (초기에는 약하게, 후기에는 강하게)
- **Early termination**: 후반부에는 일부 제약 비활성화로 계산 효율성 향상

---

## 📊 성능 및 모니터링 (Performance & Monitoring)

### 1. 디버깅 시스템

```python
class Potential(ABC):
    def __init__(self, parameters=None, debug_enabled=False):
        # Performance tracking
        self.call_count = 0
        self.gradient_call_count = 0
        
        # Configuration logging
        self._config_logged = False
        self._potential_name = self.__class__.__name__
    
    def _log_debug(self, message: str, end: bool = True) -> None:
        """Log debug message if debug logging is enabled."""
        if self.debug_enabled:
            print(f"[{self._potential_name}] {message}", end="\n" if end else " ")
    
    def get_stats(self) -> dict:
        """Get current statistics."""
        return {
            "potential_name": self._potential_name,
            "compute_calls": self.call_count,
            "gradient_calls": self.gradient_call_count,
            "debug_enabled": self.debug_enabled,
            "config_logged": self._config_logged
        }
```

### 2. 성능 분석 도구

```python
def print_potential_stats(potentials):
    """Print comprehensive statistics for all potentials."""
    print("\n=== Potential Statistics Summary ===")
    total_compute_calls = 0
    total_gradient_calls = 0
    
    for potential in potentials:
        stats = potential.get_stats()
        total_compute_calls += stats['compute_calls']
        total_gradient_calls += stats['gradient_calls']
        
        print(f"\n{stats['potential_name']}:")
        print(f"  Compute calls: {stats['compute_calls']}")
        print(f"  Gradient calls: {stats['gradient_calls']}")
        
        if stats['compute_calls'] > 0 and stats['gradient_calls'] > 0:
            ratio = stats['gradient_calls'] / stats['compute_calls']
            print(f"  Gradient/Compute ratio: {ratio:.2f}")
    
    print(f"\nTOTAL: {total_compute_calls} computes, {total_gradient_calls} gradients")
```

### 3. Activity 모니터링

```python
def compute(self, coords, feats, parameters):
    # ... (기본 계산)
    
    # Calculate total energy for runtime info and first activity detection
    total_energy = result.sum().item() if result is not None else 0.0
    
    # Log meaningful runtime info periodically
    if self.debug_enabled and result is not None and self.call_count % 50 == 1:
        active_constraints = (result > 1e-6).sum().item()
        max_energy = result.max().item()
        self._log_debug(f"Runtime: Total_energy={total_energy:.2f}, "
                       f"Active_violations={active_constraints}, Max_violation={max_energy:.4f}")
    
    # Always log if this is the first time this potential produces non-zero energy
    if (self.debug_enabled and result is not None and not hasattr(self, '_first_nonzero_logged') 
        and total_energy > 1e-6):
        self._log_debug(f"🎯 FIRST ACTIVITY: {self._potential_name} now contributing energy!")
        self._first_nonzero_logged = True
    
    return result
```

---

## 🔬 실제 사용 사례 (Real-world Usage Examples)

### 1. 단백질-리간드 복합체 예측

```yaml
# Template-based NMR constraints
constraints:
  nmr_distance:
    - chain_1: "A"
      res_1: 45
      atom_1: "CB"
      chain_2: "B" 
      res_2: 12
      atom_2: "CB"
      lower_bound: 3.5
      upper_bound: 5.0
      weight: 1.0
```

**적용 과정**:
1. **Template analysis**: 유사 구조에서 거리 정보 추출
2. **Constraint generation**: 물리적으로 의미 있는 거리 제약 생성
3. **Sampling guidance**: Diffusion 과정에서 제약 조건 강제
4. **Quality assessment**: 최종 구조의 제약 만족도 평가

### 2. 약물 분자의 입체화학 보존

```python
# Chiral center constraints from RDKit
chiral_constraints = mol.GetAtomWithIdx(atom_idx).GetChiralTag()
if chiral_constraints == Chem.rdchem.ChiralType.CHI_TETRAHEDRAL_CW:
    orientation = True  # R configuration
else:
    orientation = False  # S configuration
```

**의미**:
- **Drug efficacy**: 키랄성이 다르면 완전히 다른 생물학적 활성
- **Safety**: 잘못된 이성질체는 독성을 가질 수 있음
- **Regulatory**: 의약품 승인에서 입체화학 정확성 필수

### 3. 계산 효율성 최적화

```python
# Smart scheduling for expensive potentials
VDWOverlapPotential(
    parameters={
        "guidance_interval": 5,  # Every 5 steps instead of every step
        "guidance_weight": PiecewiseStepFunction(
            thresholds=[0.4], values=[0.125, 0.0]  # Disable after t=0.4
        ),
    }
)
```

**최적화 전략**:
- **Cost-benefit analysis**: 비용 대비 효과가 높은 시점에만 적용
- **Early stage focus**: 초기 구조 형성 시에만 VDW 충돌 방지
- **Late stage refinement**: 후반부에는 더 정밀한 제약에 집중

---

## 🚀 향후 발전 방향 (Future Directions)

### 1. 고급 제약 조건 지원

```python
# Potential future extensions
class DistanceMatrixPotential(Potential):
    """Multiple distance constraints simultaneously."""
    
class SecondaryStructurePotential(Potential):
    """α-helix, β-sheet maintenance."""
    
class SolvationPotential(Potential):
    """Implicit solvent effects."""
```

### 2. 적응적 스케줄링

```python
class AdaptiveScheduler:
    """
    Dynamically adjust potential weights based on:
    - Current violation levels
    - Convergence rate
    - Energy landscape analysis
    """
    
    def update_weights(self, current_state, violation_history):
        # Increase weight for persistently violated constraints
        # Decrease weight for well-satisfied constraints
        pass
```

### 3. 기계학습 기반 개선

```python
class LearnedPotential(Potential):
    """
    Neural network-based potential learning from:
    - Experimental data patterns
    - QM/MM calculations
    - Known protein structures
    """
```

---

## 📝 결론 (Conclusion)

Boltz의 Potentials 시스템은 구조 예측에서 **물리적 타당성**과 **화학적 정확성**을 보장하는 핵심 메커니즘입니다:

### 핵심 기여도:
1. **Multi-scale constraints**: 원자 수준부터 체인 수준까지 다양한 제약
2. **Flexible enforcement**: Hard constraint가 아닌 soft guidance로 수치적 안정성
3. **Experimental integration**: NMR, X-ray 등 실험 데이터 활용 가능
4. **Computational efficiency**: 스마트 스케줄링으로 성능 최적화

### 물리적 의미:
- **Energy minimization**: 자연계의 에너지 최소화 원리 구현
- **Thermodynamic sampling**: Boltzmann 분포를 따르는 realistic sampling
- **Chemical validity**: 기본 화학 법칙 준수로 신뢰할 수 있는 구조 생성

### 기술적 우수성:
- **Modular design**: 새로운 제약 조건 쉽게 추가 가능
- **Robust implementation**: 수치적 안정성과 에러 처리
- **Comprehensive monitoring**: 성능 추적과 디버깅 지원

이 시스템을 통해 Boltz는 단순한 확률적 생성 모델을 넘어서, **물리적으로 의미 있고 화학적으로 타당한** 분자 구조를 예측할 수 있는 강력한 도구가 되었습니다. 
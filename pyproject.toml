[build-system]
requires = ["setuptools >= 61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "boltz"
version = "2.1.1"
requires-python = ">=3.10,<3.13"
description = "Boltz"
readme = "README.md"
dependencies = [
    "torch>=2.2",
    "numpy>=1.26,<2.0",
    "hydra-core==1.3.2",
    "pytorch-lightning==2.5.0",
    "rdkit>=2024.3.2",
    "dm-tree==0.1.8",
    "requests==2.32.3",
    "pandas>=2.2.2",
    "types-requests",
    "einops==0.8.0",
    "einx==0.3.0",
    "fairscale==0.4.13",
    "mashumaro==3.14",
    "modelcif==1.2",
    "wandb==0.18.7",
    "click==8.1.7",
    "pyyaml==6.0.2",
    "biopython==1.84",
    "scipy==1.13.1",
    "numba==0.61.0",
    "gemmi==0.6.5",
    "scikit-learn==1.6.1",
    "chembl_structure_pipeline==1.2.2",
    "cuequivariance_ops_cu12>=0.5.0",
    "cuequivariance_ops_torch_cu12>=0.5.0",
    "cuequivariance_torch>=0.5.0",
]

[project.scripts]
boltz = "boltz.main:cli"

[project.optional-dependencies]
lint = ["ruff"]
test = ["pytest", "requests"]

[tool.ruff]
src = ["src"]
extend-exclude = ["conf.py"]
target-version = "py39"
lint.select = ["ALL"]
lint.ignore = [
    "COM812",  # Conflicts with the formatter
    "ISC001",  # Conflicts with the formatter
    "ANN101",  # "missing-type-self"
    "RET504",  # Unnecessary assignment to `x` before `return` statementRuff
    "S101",    # Use of `assert` detected
    "D100",    # Missing docstring in public module
    "D104",    # Missing docstring in public package
    "PT001",   # https://github.com/astral-sh/ruff/issues/8796#issuecomment-1825907715
    "PT004",   # https://github.com/astral-sh/ruff/issues/8796#issuecomment-1825907715
    "PT005",   # https://github.com/astral-sh/ruff/issues/8796#issuecomment-1825907715
    "PT023",   # https://github.com/astral-sh/ruff/issues/8796#issuecomment-1825907715
    "FBT001",
    "FBT002",
    "PLR0913", # Too many arguments to init (> 5)
]

[tool.ruff.lint.per-file-ignores]
"**/__init__.py" = [
    "F401", # Imported but unused
    "F403", # Wildcard imports
]
"docs/**" = [
    "INP001", # Requires __init__.py but folder is not a package.
]
"scripts/**" = [
    "INP001", # Requires __init__.py but folder is not a package.
]

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`(https://github.com/astral-sh/ruff/issues/5434)
keep-runtime-typing = true

[tool.ruff.lint.pydocstyle]
convention = "numpy"

[tool.pytest.ini_options]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "regression",
]

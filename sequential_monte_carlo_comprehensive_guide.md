# Sequential Monte Carlo (SMC) Methods: Comprehensive Study Guide

## Table of Contents
1. [Introduction to Sequential Monte Carlo](#1-introduction)
2. [Mathematical Foundations](#2-mathematical-foundations)
3. [Core SMC Algorithm Components](#3-core-algorithm-components)
4. [Step-by-Step SMC Algorithm](#4-step-by-step-algorithm)
5. [FK-Steering Implementation in Boltz](#5-fk-steering-implementation)
6. [Practical Applications](#6-practical-applications)
7. [Advanced Topics](#7-advanced-topics)
8. [Implementation Examples](#8-implementation-examples)

---

## 1. Introduction to Sequential Monte Carlo

### 1.1 What is Sequential Monte Carlo?

Sequential Monte Carlo (SMC) is a family of simulation-based methods for **sequential Bayesian inference**. Unlike standard Monte Carlo methods that sample from static distributions, SMC methods deal with **time-evolving probability distributions** by maintaining a set of weighted particles that approximate the target distribution.

### 1.2 Key Concepts

**Particles**: A collection of samples {x₁, x₂, ..., xₙ} representing possible states
**Weights**: Importance weights {w₁, w₂, ..., wₙ} indicating particle quality  
**Resampling**: Process of replacing low-quality particles with copies of high-quality ones
**Propagation**: Moving particles forward in time according to dynamics

### 1.3 Why Use SMC?

✅ **Sequential Nature**: Perfect for time-series and evolving systems
✅ **Non-parametric**: No assumptions about distribution shape
✅ **Parallelizable**: Particles can be processed independently
✅ **Handles Non-linearity**: Works with complex, non-linear dynamics
✅ **Multi-modal**: Can track multiple hypotheses simultaneously

---

## 2. Mathematical Foundations

### 2.1 Bayesian Sequential Estimation

The goal is to estimate the posterior distribution:
```
p(x₀:t | y₁:t) ∝ p(y₁:t | x₀:t) × p(x₀:t)
```

Where:
- `x₀:t = {x₀, x₁, ..., xₜ}`: Hidden states sequence
- `y₁:t = {y₁, y₂, ..., yₜ}`: Observations sequence

### 2.2 Sequential Importance Sampling

The key insight is to use **importance sampling** with a proposal distribution q:

```
p(x₀:t | y₁:t) ≈ Σᵢ wᵢᵗ δ(x₀:t - x₀:tᵢ)
```

Where importance weights are:
```
wᵢᵗ ∝ p(x₀:tᵢ | y₁:t) / q(x₀:tᵢ | y₁:t)
```

### 2.3 Recursive Weight Update

For computational efficiency, weights are updated recursively:
```
wᵢᵗ ∝ wᵢᵗ⁻¹ × p(yₜ | xₜᵢ) × p(xₜᵢ | xₜ₋₁ᵢ) / q(xₜᵢ | x₀:ₜ₋₁ᵢ, y₁:t)
```

---

## 3. Core SMC Algorithm Components

### 3.1 Initialization
```python
def initialize_particles(N: int) -> tuple:
    """Initialize N particles from prior distribution."""
    particles = sample_from_prior(N)  # x₀ᵢ ~ p(x₀)
    weights = np.ones(N) / N          # Equal initial weights
    return particles, weights
```

### 3.2 Prediction Step
```python
def predict_particles(particles: np.ndarray, t: int) -> np.ndarray:
    """Propagate particles forward using system dynamics."""
    new_particles = []
    for particle in particles:
        # Sample from transition model: xₜ ~ p(xₜ | xₜ₋₁)
        new_particle = sample_transition(particle, t)
        new_particles.append(new_particle)
    return np.array(new_particles)
```

### 3.3 Update Step
```python
def update_weights(particles: np.ndarray, observation: np.ndarray, 
                  weights: np.ndarray) -> np.ndarray:
    """Update particle weights based on observation likelihood."""
    for i, particle in enumerate(particles):
        # Likelihood: p(yₜ | xₜᵢ)
        likelihood = compute_likelihood(observation, particle)
        weights[i] *= likelihood
    
    # Normalize weights
    weights /= np.sum(weights)
    return weights
```

### 3.4 Resampling
```python
def resample_particles(particles: np.ndarray, weights: np.ndarray) -> tuple:
    """Resample particles based on weights to prevent degeneracy."""
    N = len(particles)
    
    # Compute effective sample size
    ESS = 1.0 / np.sum(weights**2)
    
    if ESS < N / 2:  # Resampling threshold
        # Multinomial resampling
        indices = np.random.choice(N, size=N, p=weights)
        particles = particles[indices]
        weights = np.ones(N) / N  # Reset weights
    
    return particles, weights
```

---

## 4. Step-by-Step SMC Algorithm

### 4.1 Basic SMC Framework

```python
class SequentialMonteCarlo:
    def __init__(self, N_particles: int):
        """
        Initialize SMC with N particles.
        
        Parameters
        ----------
        N_particles : int
            Number of particles to maintain
        """
        self.N = N_particles
        self.particles = None
        self.weights = None
        
    def run_smc(self, observations: list) -> dict:
        """
        Run complete SMC algorithm on sequence of observations.
        
        Parameters
        ----------
        observations : list
            Sequence of observations [y₁, y₂, ..., yₜ]
            
        Returns
        -------
        dict
            Results containing particle trajectories and weights
        """
        T = len(observations)
        particle_history = []
        weight_history = []
        
        # Step 1: Initialize
        self.particles, self.weights = self.initialize()
        
        for t in range(T):
            # Step 2: Predict (Propagate)
            self.particles = self.predict(self.particles, t)
            
            # Step 3: Update weights
            self.weights = self.update(self.particles, observations[t], self.weights)
            
            # Step 4: Resample if needed
            self.particles, self.weights = self.resample(self.particles, self.weights)
            
            # Store results
            particle_history.append(self.particles.copy())
            weight_history.append(self.weights.copy())
        
        return {
            'particles': particle_history,
            'weights': weight_history,
            'final_estimate': self.compute_estimate()
        }
```

### 4.2 Detailed Algorithm Steps

#### Step 1: Initialization
```
For i = 1 to N:
    Sample x₀ᵢ ~ p(x₀)           # Draw from prior
    Set w₀ᵢ = 1/N                # Equal weights
```

#### Step 2: For each time step t = 1, 2, ..., T:

**2a. Prediction/Propagation**
```
For i = 1 to N:
    Sample xₜᵢ ~ p(xₜ | xₜ₋₁ᵢ)   # Propagate using dynamics
```

**2b. Weight Update**
```
For i = 1 to N:
    wₜᵢ = wₜ₋₁ᵢ × p(yₜ | xₜᵢ)     # Update with likelihood
    
Normalize: wₜᵢ = wₜᵢ / Σⱼ wₜʲ
```

**2c. Resampling (if ESS < threshold)**
```
ESS = 1 / Σᵢ (wₜᵢ)²             # Effective Sample Size

If ESS < N_threshold:
    Resample particles according to weights
    Reset weights: wₜᵢ = 1/N
```

**2d. Estimation**
```
x̂ₜ = Σᵢ wₜᵢ × xₜᵢ              # Weighted average estimate
```

---

## 5. FK-Steering Implementation in Boltz

### 5.1 Overview

The boltz codebase implements **FK-steering** (Filter-Kernel steering), which combines:
1. **Energy-based particle filtering** (FK Resampling)
2. **Gradient-based guidance** for constraint satisfaction

### 5.2 Core Parameters

```python
@dataclass
class BoltzSteeringParams:
    """FK-steering configuration in boltz."""
    fk_steering: bool = True           # Enable FK resampling
    num_particles: int = 3             # Number of particles
    fk_lambda: float = 4.0             # Temperature parameter
    fk_resampling_interval: int = 3    # Resampling frequency
    guidance_update: bool = True       # Enable gradient guidance
    num_gd_steps: int = 20            # Gradient descent steps
```

### 5.3 FK-Steering in Protein Structure Prediction

```python
def sample_with_fk_steering(self, atom_mask, steering_args, **kwargs):
    """
    Diffusion sampling with FK-steering for protein structure prediction.
    
    Process:
    1. Initialize multiple particles (protein structures)
    2. For each diffusion denoising step:
       a. Network prediction: noise → structure
       b. Compute potential energies (constraints)
       c. FK Resampling: filter high-energy structures
       d. Gradient Guidance: optimize structure
    3. Return final structure
    """
    potentials = get_potentials()  # Physical constraints
    
    if steering_args["fk_steering"]:
        multiplicity = multiplicity * steering_args["num_particles"]
        energy_traj = torch.empty((multiplicity, 0), device=self.device)
    
    # Main denoising loop
    for step_idx, (sigma_t, sigma_next, gamma_next) in enumerate(sigmas_and_gammas):
        
        # 1. Network prediction: noise → structure
        pred_eps, token_repr = self.preconditioned_network_forward(
            atom_coords, sigma_t, network_condition_kwargs, training=False
        )
        atom_coords_denoised = self.c_skip(sigma_t) * atom_coords + self.c_out(sigma_t) * pred_eps
        
        # 2. FK Resampling: energy-based particle filtering
        if steering_args["fk_steering"] and (step_idx % steering_args["fk_resampling_interval"] == 0):
            energy = torch.zeros(multiplicity, device=self.device)
            
            # Compute total energy from all potentials
            for potential in potentials:
                parameters = potential.compute_parameters(steering_t)
                if parameters["resampling_weight"] > 0:
                    component_energy = potential.compute(
                        atom_coords_denoised, feats, parameters
                    )
                    energy += parameters["resampling_weight"] * component_energy
            
            # Energy improvement metric
            log_G = energy_traj[:, -2] - energy_traj[:, -1] if step_idx > 0 else -energy
            
            # Boltzmann-like resampling weights
            resample_weights = F.softmax(
                (ll_difference + steering_args["fk_lambda"] * log_G).reshape(
                    -1, steering_args["num_particles"]
                ), dim=1
            )
        
        # 3. Gradient Guidance: direct energy minimization
        if steering_args["guidance_update"] and step_idx < num_sampling_steps - 1:
            guidance_update = torch.zeros_like(atom_coords_denoised)
            
            for guidance_step in range(steering_args["num_gd_steps"]):
                energy_gradient = torch.zeros_like(atom_coords_denoised)
                
                for potential in potentials:
                    parameters = potential.compute_parameters(steering_t)
                    if (parameters["guidance_weight"] > 0 and 
                        guidance_step % parameters["guidance_interval"] == 0):
                        energy_gradient += parameters["guidance_weight"] * potential.compute_gradient(
                            atom_coords_denoised + guidance_update, feats, parameters
                        )
                
                # Gradient descent step: x ← x - α∇E(x)
                guidance_update -= energy_gradient
            
            atom_coords_denoised += guidance_update
        
        # 4. Multinomial resampling
        if steering_args["fk_steering"]:
            resample_indices = torch.multinomial(
                resample_weights, 
                resample_weights.shape[1], 
                replacement=True
            ).flatten()
            
            # Resample all particle-related tensors
            atom_coords = atom_coords[resample_indices]
            atom_coords_denoised = atom_coords_denoised[resample_indices]
            energy_traj = energy_traj[resample_indices]
```

### 5.4 Physical Interpretation

#### 5.4.1 Energy-Based Filtering
- **Boltzmann Factor**: `exp(-βE)` gives higher probability to lower energy states
- **Physical Constraints**: Multiple potential functions enforce:
  - Bond lengths and angles
  - Van der Waals interactions  
  - Chirality constraints
  - NMR distance constraints

#### 5.4.2 Multi-Objective Optimization
```python
# Total energy combines multiple constraints
E_total = Σᵢ wᵢ × Eᵢ(structure)

Where:
- E_bond: Bond length violations
- E_angle: Bond angle violations  
- E_vdw: Van der Waals clashes
- E_nmr: NMR distance constraint violations
```

---

## 6. Practical Applications

### 6.1 Protein Structure Prediction
- **Problem**: Predict 3D structure from amino acid sequence
- **SMC Role**: Filter structures that violate physical constraints
- **Particles**: Different protein conformations
- **Weights**: Based on energy functions (physics-based scoring)

### 6.2 Object Tracking
- **Problem**: Track object position over time from noisy observations
- **SMC Role**: Maintain multiple hypotheses about object location
- **Particles**: Possible object positions/velocities
- **Weights**: Based on observation likelihood

### 6.3 Financial Time Series
- **Problem**: Estimate hidden market states from price observations
- **SMC Role**: Track regime changes and volatility
- **Particles**: Different market state hypotheses
- **Weights**: Based on price prediction accuracy

---

## 7. Advanced Topics

### 7.1 Particle Degeneracy Problem

**Issue**: Over time, most particles have negligible weights
**Solutions**:
- Adaptive resampling (ESS-based)
- Regularization techniques
- Auxiliary particle filters

### 7.2 Resampling Strategies

#### Multinomial Resampling
```python
indices = np.random.choice(N, size=N, p=weights)
```

#### Systematic Resampling  
```python
def systematic_resampling(weights):
    N = len(weights)
    u = np.random.uniform(0, 1/N)
    indices = []
    cumsum = np.cumsum(weights)
    
    for i in range(N):
        while cumsum[len(indices)] < u:
            indices.append(len(indices))
        u += 1/N
    return indices
```

#### Stratified Resampling
- Reduces Monte Carlo variance
- Better preservation of particle diversity

### 7.3 Adaptive Parameters

```python
class AdaptiveSMC:
    def adapt_num_particles(self, ESS_threshold=0.5):
        """Dynamically adjust number of particles based on ESS."""
        current_ESS = 1.0 / np.sum(self.weights**2)
        if current_ESS / self.N < ESS_threshold:
            self.N = int(self.N * 1.5)  # Increase particles
            
    def adapt_resampling_threshold(self, variance_estimate):
        """Adjust resampling frequency based on system dynamics."""
        if variance_estimate > self.threshold:
            self.resample_interval = max(1, self.resample_interval - 1)
```

---

## 8. Implementation Examples

### 8.1 Simple 1D Tracking Example

```python
import numpy as np
import matplotlib.pyplot as plt

class SimpleTracker:
    def __init__(self, N=100):
        """Simple 1D object tracker using SMC."""
        self.N = N
        self.process_noise = 0.1
        self.observation_noise = 0.5
        
    def run_tracking(self, observations):
        """Track object through sequence of noisy observations."""
        T = len(observations)
        particles = np.random.normal(0, 1, self.N)  # Initialize
        weights = np.ones(self.N) / self.N
        
        estimates = []
        
        for t in range(T):
            # Predict: x_t = x_{t-1} + noise
            particles += np.random.normal(0, self.process_noise, self.N)
            
            # Update weights based on observation likelihood
            for i in range(self.N):
                likelihood = np.exp(-0.5 * ((observations[t] - particles[i]) / self.observation_noise)**2)
                weights[i] *= likelihood
            
            # Normalize weights
            weights /= np.sum(weights)
            
            # Estimate: weighted average
            estimate = np.sum(weights * particles)
            estimates.append(estimate)
            
            # Resample if needed
            ESS = 1.0 / np.sum(weights**2)
            if ESS < self.N / 2:
                indices = np.random.choice(self.N, self.N, p=weights)
                particles = particles[indices]
                weights = np.ones(self.N) / self.N
        
        return estimates

# Example usage
true_positions = np.cumsum(np.random.normal(0, 0.1, 50))  # True trajectory
observations = true_positions + np.random.normal(0, 0.5, 50)  # Noisy observations

tracker = SimpleTracker(N=200)
estimates = tracker.run_tracking(observations)

# Plot results
plt.figure(figsize=(12, 6))
plt.plot(true_positions, 'g-', label='True Position', linewidth=2)
plt.plot(observations, 'r.', label='Observations', alpha=0.6)
plt.plot(estimates, 'b-', label='SMC Estimates', linewidth=2)
plt.legend()
plt.xlabel('Time')
plt.ylabel('Position')
plt.title('Sequential Monte Carlo Tracking Example')
plt.grid(True, alpha=0.3)
plt.show()
```

### 8.2 Energy-Based Particle Filter (Boltz-Style)

```python
class EnergyBasedFilter:
    def __init__(self, N=50, lambda_energy=4.0):
        """Energy-based particle filter similar to boltz FK-steering."""
        self.N = N
        self.lambda_energy = lambda_energy
        
    def energy_function(self, particles, constraints):
        """Compute energy for constraint violations."""
        energies = np.zeros(len(particles))
        
        for i, particle in enumerate(particles):
            energy = 0.0
            for constraint in constraints:
                violation = max(0, constraint['threshold'] - constraint['func'](particle))
                energy += constraint['weight'] * violation**2
            energies[i] = energy
            
        return energies
    
    def fk_resampling(self, particles, energies, prev_energies=None):
        """FK resampling based on energy improvements."""
        if prev_energies is not None:
            # Energy improvement (log G in boltz code)
            log_G = prev_energies - energies
        else:
            # First step: use negative energy
            log_G = -energies
        
        # Boltzmann-like weights
        weights = np.exp(self.lambda_energy * log_G)
        weights /= np.sum(weights)
        
        # Multinomial resampling
        indices = np.random.choice(len(particles), len(particles), p=weights)
        return particles[indices], energies[indices]
    
    def gradient_guidance(self, particles, constraints, num_steps=10, step_size=0.01):
        """Gradient-based constraint satisfaction."""
        for step in range(num_steps):
            gradients = np.zeros_like(particles)
            
            for constraint in constraints:
                for i, particle in enumerate(particles):
                    # Numerical gradient
                    eps = 1e-6
                    grad = (constraint['func'](particle + eps) - constraint['func'](particle - eps)) / (2 * eps)
                    
                    if constraint['func'](particle) < constraint['threshold']:
                        gradients[i] += constraint['weight'] * grad
            
            # Gradient descent step
            particles -= step_size * gradients
        
        return particles

# Example: Constrained sampling
def example_constrained_sampling():
    """Example of energy-based filtering with constraints."""
    filter_obj = EnergyBasedFilter(N=100, lambda_energy=2.0)
    
    # Define constraints (example: stay within bounds)
    constraints = [
        {'func': lambda x: abs(x), 'threshold': 2.0, 'weight': 1.0},  # |x| < 2
        {'func': lambda x: x**2, 'threshold': 1.0, 'weight': 0.5}     # x² < 1
    ]
    
    # Initialize particles
    particles = np.random.normal(0, 2, filter_obj.N)
    prev_energies = None
    
    results = []
    
    for step in range(20):
        # Compute energies
        energies = filter_obj.energy_function(particles, constraints)
        
        # FK resampling every 3 steps
        if step % 3 == 0:
            particles, energies = filter_obj.fk_resampling(particles, energies, prev_energies)
        
        # Gradient guidance
        particles = filter_obj.gradient_guidance(particles, constraints)
        
        # Store results
        results.append({
            'particles': particles.copy(),
            'energies': energies.copy(),
            'mean': np.mean(particles),
            'std': np.std(particles)
        })
        
        prev_energies = energies.copy()
    
    return results

# Run example
results = example_constrained_sampling()

# Plot evolution
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

steps = range(len(results))
means = [r['mean'] for r in results]
stds = [r['std'] for r in results]
energies = [np.mean(r['energies']) for r in results]

ax1.plot(steps, means, 'b-', label='Mean Position')
ax1.fill_between(steps, np.array(means) - np.array(stds), 
                 np.array(means) + np.array(stds), alpha=0.3, label='±1 Std')
ax1.set_xlabel('Step')
ax1.set_ylabel('Particle Position')
ax1.set_title('Particle Evolution with Constraints')
ax1.legend()
ax1.grid(True, alpha=0.3)

ax2.plot(steps, energies, 'r-', linewidth=2)
ax2.set_xlabel('Step')
ax2.set_ylabel('Average Energy')
ax2.set_title('Energy Evolution (Constraint Violations)')
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
```

---

## Summary

Sequential Monte Carlo methods provide a powerful framework for:

1. **Sequential Bayesian inference** in time-evolving systems
2. **Non-parametric estimation** without distributional assumptions  
3. **Multi-hypothesis tracking** through particle representations
4. **Constraint satisfaction** via energy-based resampling

The **FK-steering approach in boltz** demonstrates a sophisticated application of SMC principles to protein structure prediction, combining:
- Probabilistic particle filtering for structural diversity
- Energy-based resampling for constraint satisfaction
- Gradient guidance for local optimization

This hybrid approach leverages the best of both worlds: the global exploration capabilities of SMC and the precision of gradient-based optimization.

**Key Takeaways**:
- SMC excels in sequential, non-linear, and multi-modal problems
- Resampling prevents particle degeneracy but requires careful tuning
- Energy-based filtering naturally incorporates domain knowledge
- Hybrid approaches (SMC + optimization) often outperform pure methods

For further study, explore advanced topics like:
- Auxiliary particle filters
- Rao-Blackwellized particle filters  
- Adaptive SMC methods
- SMC² (SMC for parameter estimation) 
import numpy as np
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Any, Union, Callable
from Bio import PDB
from Bio.PDB import <PERSON><PERSON><PERSON><PERSON><PERSON>, MMCIFParser
from Bio.SeqUtils import seq1
import warnings
from dataclasses import dataclass

from boltz.data.types import MinDistance
from boltz.data.parse.struct2seq import StructureSequenceMapper


def calculate_sequence_identity(
    aligned_seq1: str, aligned_seq2: str
    ) -> float:
    """
    Calculate sequence identity from aligned sequences.
    
    Parameters
    ----------
    aligned_seq1 : str
        First aligned sequence
    aligned_seq2 : str
        Second aligned sequence
        
    Returns
    -------
    float
        Sequence identity (0.0 to 1.0)
    """
    if len(aligned_seq1) != len(aligned_seq2):
        return 0.0
        
    matches = sum(1 for a, b in zip(aligned_seq1, aligned_seq2) 
        if a == b and a != '-' and b != '-'
    )
    aligned_length = sum(1 for a, b in zip(aligned_seq1, aligned_seq2) 
        if a != '-' and b != '-'
    )
    return matches / aligned_length if aligned_length > 0 else 0.0


@dataclass
class SequenceMappingResult:
    """
    Result of sequence mapping between template and query.
    
    Attributes
    ----------
    aligned_struct : str
        Aligned template structure sequence
    aligned_given : str
        Aligned query sequence
    mapping : List[Tuple[int, int]]
        Mapping from query indices to template indices
    seq_identity : float
        Sequence identity between aligned sequences
    mapping_dict : Dict[int, int]
        Dictionary version of mapping for fast lookup
    """
    aligned_struct: str
    aligned_given: str
    mapping: List[Tuple[int, int]]
    seq_identity: float
    mapping_dict: Dict[int, int]
    
    @classmethod
    def from_mapper_result(
        cls, 
        aligned_struct: str, 
        aligned_given: str, 
        mapping: List[Tuple[int, int]], 
        stats: Any
    ) -> 'SequenceMappingResult':
        """Create SequenceMappingResult from mapper output."""
        seq_identity = calculate_sequence_identity(aligned_struct, aligned_given)
        mapping_dict = dict(mapping)
        return cls(aligned_struct, aligned_given, mapping, seq_identity, mapping_dict)


class TemplateConstraintGenerator:
    """
    Template-based distance constraint generator for protein structure prediction.
    
    This class generates multiple distance constraints based on template structures
    to prevent non-physical conformations when using single atom pair constraints.
    """
    
    def __init__(
        self, 
        distance_threshold: float = 20.0,
        cb_distance_cutoff: float = 50.0,
        min_sequence_identity: float = 0.6,
        gap_penalty: float = -2.0,
        disulfide_distance_threshold: float = 2.2,
        include_disulfide: bool = True
    ):
        """
        Initialize the template constraint generator.
        
        Parameters
        ----------
        distance_threshold : float
            Maximum distance to consider for constraints (Angstroms)
        cb_distance_cutoff : float
            Maximum Cb-Cb distance for constraint generation (Angstroms)
        min_sequence_identity : float
            Minimum sequence identity for reliable alignment
        gap_penalty : float
            Gap penalty for sequence alignment
        disulfide_distance_threshold : float, default=2.2
            Maximum distance (Angstroms) to consider as disulfide bond
        include_disulfide : bool, default=True
            Whether to include disulfide bond constraints
        """
        self.distance_threshold = distance_threshold
        self.cb_distance_cutoff = cb_distance_cutoff
        self.min_sequence_identity = min_sequence_identity
        self.disulfide_distance_threshold = disulfide_distance_threshold
        self.include_disulfide = include_disulfide
        self.mapper = StructureSequenceMapper(gap_penalty=gap_penalty)
        
        print(f"  INFO: cb_distance_cutoff: {cb_distance_cutoff} (Angstroms)")
        if include_disulfide:
            print(f"  INFO: disulfide_distance_threshold: {disulfide_distance_threshold} (Angstroms)")
    
    def _extract_cb_coordinates(
        self, 
        structure_file: str, 
        chain_id: str
    ) -> Dict[int, np.ndarray]:
        """
        Extract Cb coordinates from template structure.
        
        Parameters
        ----------
        structure_file : str
            Path to template structure file
        chain_id : str
            Chain identifier
            
        Returns
        -------
        Dict[int, np.ndarray]
            Dictionary mapping residue index to Cb coordinates
        """
        try:
            structure = self.mapper._get_structure_parser(structure_file)
            cb_coords = {}
            
            for model in structure:
                for chain in model:
                    if chain.id == chain_id:
                        residue_idx = 0
                        for residue in chain:
                            if residue.id[0] == " ":  # Standard residue                                
                                res_name = residue.resname.strip() # Get residue name
                                
                                cb_atom = None
                                #if (res_name == "GLY") or (res_name == "PRO"):                                     
                                if res_name == "GLY":
                                    cb_atom = residue['CA']
                                else:                                    
                                    if 'CB' in residue: # For other residues, try CB first
                                        cb_atom = residue['CB']                                    
                                    else:
                                        warnings.warn(f"Missing both CB and CA atoms in {res_name} residue at position {residue_idx}")
                                
                                if cb_atom is not None:
                                    cb_coords[residue_idx] = np.array(cb_atom.get_coord())
                                else:
                                    warnings.warn(f"Missing CB atom in {res_name}{residue_idx} residue")

                                residue_idx += 1
                        break
                break
                        
            return cb_coords
            
        except Exception as e:
            warnings.warn(f"Failed to extract Cb coordinates: {e}")
            return {}
    
    def _extract_sulfur_coordinates(
        self, 
        structure_file: str, 
        chain_id: str
    ) -> Dict[int, np.ndarray]:
        """
        Extract SG coordinates from CYS residues for disulfide bond detection.
        
        Parameters
        ----------
        structure_file : str
            Path to template structure file
        chain_id : str
            Chain identifier
            
        Returns
        -------
        Dict[int, np.ndarray]
            Dictionary mapping residue index to SG coordinates for CYS residues only
        """
        try:
            structure = self.mapper._get_structure_parser(structure_file)
            sg_coords = {}
            
            for model in structure:
                for chain in model:
                    if chain.id == chain_id:
                        residue_idx = 0
                        for residue in chain:
                            if residue.id[0] == " ":  # Standard residue
                                res_name = residue.resname.strip()
                                
                                # Only process CYS residues
                                if res_name == "CYS":
                                    if 'SG' in residue:
                                        sg_atom = residue['SG']
                                        sg_coords[residue_idx] = np.array(sg_atom.get_coord())
                                    else:
                                        warnings.warn(f"Missing SG atom in CYS residue at position {residue_idx}")
                                
                                residue_idx += 1
                        break
                break
                        
            return sg_coords
            
        except Exception as e:
            warnings.warn(f"Failed to extract SG coordinates: {e}")
            return {}
    
    def _compute_distance_pairs(
        self, 
        coords: Dict[int, np.ndarray],
        distance_filter: Callable[[float], bool],
        return_format: str = "map"
    ) -> Union[Dict[Tuple[int, int], float], List[Tuple[int, int, float]]]:
        """
        Compute distance pairs from coordinates with filtering.
        
        Parameters
        ----------
        coords : Dict[int, np.ndarray]
            Dictionary mapping residue index to coordinates
        distance_filter : Callable[[float], bool]
            Function to filter distances (returns True if distance should be included)
        return_format : str, default="map"
            Return format: "map" for dict, "bonds" for list
            
        Returns
        -------
        Union[Dict[Tuple[int, int], float], List[Tuple[int, int, float]]]
            Distance pairs in requested format
        """
        results = []
        residue_indices = list(coords.keys())
        
        for i, idx1 in enumerate(residue_indices):
            for idx2 in residue_indices[i+1:]:  # Avoid duplicate pairs
                if idx1 in coords and idx2 in coords:
                    coord1 = coords[idx1]
                    coord2 = coords[idx2]
                    distance = np.linalg.norm(coord1 - coord2)
                    
                    if distance_filter(distance):
                        results.append((idx1, idx2, distance))
        
        if return_format == "map":
            return {(idx1, idx2): distance for idx1, idx2, distance in results}
        elif return_format == "bonds":
            return results
        else:
            raise ValueError(f"Unknown return format: {return_format}")
    

    
    def _prepare_sequence_mapping(
        self,
        template_structure: str,
        template_chain_id: str,
        query_sequence: str
    ) -> Optional[SequenceMappingResult]:
        """
        Prepare sequence mapping between template and query sequences.
        
        Parameters
        ----------
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_sequence : str
            Query protein sequence
            
        Returns
        -------
        Optional[SequenceMappingResult]
            Sequence mapping result, or None if mapping fails or identity is too low
        """
        try:
            # Map sequences
            aligned_struct, aligned_given, mapping, stats = self.mapper.map_sequences(
                template_structure, template_chain_id, query_sequence
            )
            
            # Create mapping result
            mapping_result = SequenceMappingResult.from_mapper_result(
                aligned_struct, aligned_given, mapping, stats
            )
            
            print(f"  INFO: Sequence identity: {mapping_result.seq_identity:.3f}")
            
            # Check sequence identity threshold
            if mapping_result.seq_identity < self.min_sequence_identity:
                warnings.warn(
                    f"Low sequence identity ({mapping_result.seq_identity:.3f}) "
                    f"may lead to unreliable constraints"
                )
                return None
            
            return mapping_result
            
        except Exception as e:
            warnings.warn(f"Failed to prepare sequence mapping: {e}")
            return None
    
    def _create_constraint_from_mapping(
        self,
        template_idx1: int,
        template_idx2: int,
        distance: float,
        mapping_result: SequenceMappingResult,
        query_sequence: str,
        query_chain_id: str,
        constraint_type: str,
        distance_buffer: float,
        base_weight: float,
        sequence_identity_weight: bool,
        atom1_name: str = None,
        atom2_name: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        Create constraint from template mapping information.
        
        Parameters
        ----------
        template_idx1, template_idx2 : int
            Template residue indices
        distance : float
            Distance between atoms/residues
        mapping_result : SequenceMappingResult
            Sequence mapping information
        query_sequence : str
            Query protein sequence
        query_chain_id : str
            Query chain identifier
        constraint_type : str
            Type of constraint ("min_distance", "nmr_distance", "bond")
        distance_buffer : float
            Distance buffer for bounds
        base_weight : float
            Base weight for constraints
        sequence_identity_weight : bool
            Whether to apply sequence identity weighting
        atom1_name, atom2_name : str, optional
            Specific atom names (if None, will determine based on residue type)
            
        Returns
        -------
        Optional[Dict[str, Any]]
            Constraint dictionary, or None if mapping fails
        """
        # Find corresponding query indices
        query_idx1 = None
        query_idx2 = None
        
        for query_idx, template_idx in mapping_result.mapping_dict.items():
            if template_idx == template_idx1:
                query_idx1 = query_idx
            elif template_idx == template_idx2:
                query_idx2 = query_idx
        
        if query_idx1 is None or query_idx2 is None:
            return None
        
        # Determine atom names if not provided
        if atom1_name is None:
            atom1_name = "CA" if query_sequence[query_idx1] == "G" else "CB"
        if atom2_name is None:
            atom2_name = "CA" if query_sequence[query_idx2] == "G" else "CB"
        
        # Generate constraint based on type
        if constraint_type == "min_distance":
            constraint = {
                "min_distance": {
                    "atom1": [query_chain_id, query_idx1 + 1, atom1_name],  # 1-indexed
                    "atom2": [query_chain_id, query_idx2 + 1, atom2_name],  # 1-indexed
                    "distance": float(distance)
                }
            }
        elif constraint_type == "nmr_distance":
            # Calculate bounds with buffer
            lower_bound = max(0.0, distance * (1 - distance_buffer))
            upper_bound = distance * (1 + distance_buffer)
            
            # Calculate weight
            weight = base_weight
            if sequence_identity_weight:
                weight *= mapping_result.seq_identity
            
            constraint = {
                "nmr_distance": {
                    "atom1": [query_chain_id, query_idx1 + 1, atom1_name],  # 1-indexed
                    "atom2": [query_chain_id, query_idx2 + 1, atom2_name],  # 1-indexed
                    "lower_bound": float(lower_bound),
                    "upper_bound": float(upper_bound),
                    "weight": float(weight)
                }
            }
        elif constraint_type == "bond":
            constraint = {
                "bond": {
                    "atom1": [query_chain_id, query_idx1 + 1, atom1_name],  # 1-indexed
                    "atom2": [query_chain_id, query_idx2 + 1, atom2_name]   # 1-indexed
                }
            }
        else:
            raise ValueError(f"Unknown constraint type: {constraint_type}")
        
        return constraint
    
    def generate_cb_cb_constraints(
        self,
        query_sequence: str,
        template_structure: str,
        template_chain_id: str,
        query_chain_id: str,
        constraint_type: str,
        distance_buffer: float,
        base_weight: float,
        sequence_identity_weight: bool
    ) -> List[Dict[str, Any]]:
        """
        Generate Cb-Cb distance constraints from template structure.
        
        Parameters
        ----------
        query_sequence : str
            Query protein sequence
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_chain_id : str
            Query chain identifier
        constraint_type : str
            Type of constraint ("min_distance" or "nmr_distance")
        distance_buffer : float
            Distance buffer for NMR bounds
        base_weight : float
            Base weight for constraints
        sequence_identity_weight : bool
            Whether to apply sequence identity weighting
            
        Returns
        -------
        List[Dict[str, Any]]
            List of Cb-Cb constraint dictionaries
        """
        # Prepare sequence mapping
        mapping_result = self._prepare_sequence_mapping(
            template_structure, template_chain_id, query_sequence
        )
        if mapping_result is None:
            return []
        
        # Extract CB coordinates from template
        cb_coords = self._extract_cb_coordinates(template_structure, template_chain_id)
        if not cb_coords:
            return []
        
        # Compute distance map using generic method with Cb-specific filter
        distance_filter = lambda d: 0.0 <= d <= self.cb_distance_cutoff
        distance_map = self._compute_distance_pairs(cb_coords, distance_filter, return_format="map")
        if not distance_map:
            return []
        
        # Generate constraints based on mapping
        constraints = []
        for (template_idx1, template_idx2), distance in distance_map.items():
            constraint = self._create_constraint_from_mapping(
                template_idx1=template_idx1,
                template_idx2=template_idx2,
                distance=distance,
                mapping_result=mapping_result,
                query_sequence=query_sequence,
                query_chain_id=query_chain_id,
                constraint_type=constraint_type,
                distance_buffer=distance_buffer,
                base_weight=base_weight,
                sequence_identity_weight=sequence_identity_weight
            )
            
            if constraint is not None:
                constraints.append(constraint)
        
        return constraints
    
    def generate_disulfide_constraints_internal(
        self,
        query_sequence: str,
        template_structure: str,
        template_chain_id: str,
        query_chain_id: str,
        disulfide_distance_threshold: float,
        distance_buffer: float,
        base_weight: float,
        sequence_identity_weight: bool,
        constraint_type: str
    ) -> List[Dict[str, Any]]:
        """
        Internal method to generate disulfide constraints from template structure.
        
        Parameters
        ----------
        query_sequence : str
            Query protein sequence
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_chain_id : str
            Query chain identifier
        disulfide_distance_threshold : float
            Maximum distance to consider as disulfide bond
        distance_buffer : float
            Distance buffer for NMR bounds
        base_weight : float
            Base weight for constraints
        sequence_identity_weight : bool
            Whether to apply sequence identity weighting
        constraint_type : str
            Type of constraint ("nmr_distance", "bond", or "both")
            
        Returns
        -------
        List[Dict[str, Any]]
            List of disulfide constraint dictionaries
        """
        # Prepare sequence mapping
        mapping_result = self._prepare_sequence_mapping(
            template_structure, template_chain_id, query_sequence
        )
        if mapping_result is None:
            return []
        
        # Extract SG coordinates from template
        sg_coords = self._extract_sulfur_coordinates(template_structure, template_chain_id)
        if not sg_coords:
            print(f"  INFO: No CYS residues found in template")
            return []
        
        # Find disulfide bonds using generic method with disulfide-specific filter
        distance_filter = lambda d: d <= disulfide_distance_threshold
        disulfide_bonds = self._compute_distance_pairs(sg_coords, distance_filter, return_format="bonds")
        if not disulfide_bonds:
            print(f"  INFO: No disulfide bonds found in template")
            return []
        
        print(f"  INFO: Found {len(disulfide_bonds)} disulfide bonds in template")
        
        # Generate constraints based on mapping
        constraints = []
        for template_idx1, template_idx2, distance in disulfide_bonds:
            # Check if both residues are CYS in query sequence
            query_idx1 = mapping_result.mapping_dict.get(template_idx1)
            query_idx2 = mapping_result.mapping_dict.get(template_idx2)
            
            if (query_idx1 is not None and query_idx2 is not None and 
                query_idx1 < len(query_sequence) and query_idx2 < len(query_sequence) and
                query_sequence[query_idx1] == 'C' and query_sequence[query_idx2] == 'C'):
                
                # Generate constraints based on type
                constraint_types = []
                if constraint_type in ["nmr_distance", "both"]:
                    constraint_types.append("nmr_distance")
                if constraint_type in ["bond", "both"]:
                    constraint_types.append("bond")
                
                for c_type in constraint_types:
                    # Special handling for disulfide bonds with SG atoms
                    if c_type == "nmr_distance":
                        # Disulfide specific bounds
                        lower_bound = max(2.0, distance * (1 - distance_buffer))
                        upper_bound = distance * (1 + distance_buffer)
                        
                        weight = base_weight
                        if sequence_identity_weight:
                            weight *= mapping_result.seq_identity
                        
                        constraint = {
                            "nmr_distance": {
                                "atom1": [query_chain_id, query_idx1 + 1, "SG"],  # 1-indexed
                                "atom2": [query_chain_id, query_idx2 + 1, "SG"],  # 1-indexed  
                                "lower_bound": float(lower_bound),
                                "upper_bound": float(upper_bound),
                                "weight": float(weight)
                            }
                        }
                        constraints.append(constraint)
                        print(f"    Generated S-S NMR constraint: CYS{query_idx1+1}-CYS{query_idx2+1} "
                              f"({lower_bound:.2f}-{upper_bound:.2f}Å, weight={weight:.3f})")
                    
                    elif c_type == "bond":
                        constraint = {
                            "bond": {
                                "atom1": [query_chain_id, query_idx1 + 1, "SG"],  # 1-indexed
                                "atom2": [query_chain_id, query_idx2 + 1, "SG"]   # 1-indexed
                            }
                        }
                        constraints.append(constraint)
                        print(f"    Generated S-S bond constraint: CYS{query_idx1+1}-CYS{query_idx2+1}")
        
        return constraints
    
    def generate_template_constraints(
        self,
        query_sequence: str,
        template_structure: str,
        template_chain_id: str,
        query_chain_id: str = "A",
        constraint_type: str = "nmr_distance",
        distance_buffer: float = 0.1,
        base_weight: float = 1.0,
        sequence_identity_weight: bool = True,
        include_disulfide: Optional[bool] = None,
        include_disulfide_nmr: bool = True,
        include_disulfide_bond: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Generate template-based distance constraints including Cb-Cb and optionally S-S constraints.
        
        Parameters
        ----------
        query_sequence : str
            Query protein sequence
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_chain_id : str
            Query chain identifier (default: "A")
        constraint_type : str
            Type of constraint to generate ("min_distance" or "nmr_distance", default: "nmr_distance")
        distance_buffer : float
            Buffer percentage for NMR bounds (default: 0.1 = 10%)
        base_weight : float
            Base weight for NMR constraints (default: 1.0)
        sequence_identity_weight : bool
            Whether to scale weight by sequence identity (default: True)
        include_disulfide : Optional[bool]
            Whether to include disulfide constraints (default: use class setting, deprecated)
        include_disulfide_nmr : bool, default=True
            Whether to include disulfide NMR distance constraints
        include_disulfide_bond : bool, default=True
            Whether to include disulfide bond constraints
            
        Returns
        -------
        List[Dict[str, Any]]
            List of constraint dictionaries compatible with boltz schema
        """
        try:
            # Use class setting if not explicitly provided (backward compatibility)
            if include_disulfide is None:
                include_disulfide = self.include_disulfide
            
            # Generate Cb-Cb constraints
            constraints = self.generate_cb_cb_constraints(
                query_sequence=query_sequence,
                template_structure=template_structure,
                template_chain_id=template_chain_id,
                query_chain_id=query_chain_id,
                constraint_type=constraint_type,
                distance_buffer=distance_buffer,
                base_weight=base_weight,
                sequence_identity_weight=sequence_identity_weight
            )
            
            cb_count = len(constraints)
            print(f"  INFO: Generated {cb_count} Cb-Cb template constraints")
            
            # Generate disulfide constraints if enabled (backward compatibility)
            if include_disulfide and (include_disulfide_nmr or include_disulfide_bond):
                # Determine constraint type for disulfide bonds
                disulfide_constraint_type = []
                if include_disulfide_nmr:
                    disulfide_constraint_type.append("nmr_distance")
                if include_disulfide_bond:
                    disulfide_constraint_type.append("bond")
                
                # Generate constraints for each type
                for d_constraint_type in disulfide_constraint_type:
                    disulfide_constraints = self.generate_disulfide_constraints_internal(
                        query_sequence=query_sequence,
                        template_structure=template_structure,
                        template_chain_id=template_chain_id,
                        query_chain_id=query_chain_id,
                        disulfide_distance_threshold=self.disulfide_distance_threshold,
                        distance_buffer=distance_buffer,
                        base_weight=base_weight,
                        sequence_identity_weight=sequence_identity_weight,
                        constraint_type=d_constraint_type
                    )
                    constraints.extend(disulfide_constraints)
                
                print(f"  INFO: Added disulfide constraints (NMR: {include_disulfide_nmr}, Bond: {include_disulfide_bond})")
            
            print(f"  INFO: Total template constraints generated: {len(constraints)}")
            return constraints
            
        except Exception as e:
            warnings.warn(f"Failed to generate template constraints: {e}")
            return []
    
    def generate_constraints_for_boltz_schema(
        self,
        schema_data: Dict[str, Any],
        template_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate constraints and integrate into boltz schema.
        
        Parameters
        ----------
        schema_data : Dict[str, Any]
            Original boltz schema data
        template_info : Dict[str, Any]
            Template information
            
        Returns
        -------
        Dict[str, Any]
            Schema data with added template constraints
        """
        try:
            # Extract template constraints
            constraints = self.generate_template_constraints(**template_info)
            
            # Add to schema
            if "constraints" not in schema_data:
                schema_data["constraints"] = []
            
            schema_data["constraints"].extend(constraints)
            
            return schema_data
            
        except Exception as e:
            warnings.warn(f"Failed to integrate template constraints: {e}")
            return schema_data

    def generate_disulfide_constraints(
        self,
        query_sequence: str,
        template_structure: str,
        template_chain_id: str,
        query_chain_id: str = "A",
        disulfide_distance_threshold: float = 2.2,
        distance_buffer: float = 0.05,
        base_weight: float = 5.0,
        sequence_identity_weight: bool = True,
        constraint_type: str = "both"
    ) -> List[Dict[str, Any]]:
        """
        Generate disulfide bond constraints from template structure.
        
        Parameters
        ----------
        query_sequence : str
            Query protein sequence
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_chain_id : str
            Query chain identifier (default: "A")
        disulfide_distance_threshold : float, default=2.2
            Maximum distance (Angstroms) to consider as disulfide bond
        distance_buffer : float, default=0.1
            Buffer percentage for NMR bounds (default: 0.1 = 10%)
        base_weight : float, default=1.0
            Base weight for NMR constraints
        sequence_identity_weight : bool, default=True
            Whether to scale weight by sequence identity
        constraint_type : str, default="nmr_distance"
            Type of constraint to generate ("nmr_distance", "bond", or "both")
            
        Returns
        -------
        List[Dict[str, Any]]
            List of disulfide constraint dictionaries in boltz schema format
        """
        try:
            # Use the internal method for actual constraint generation
            constraints = self.generate_disulfide_constraints_internal(
                query_sequence=query_sequence,
                template_structure=template_structure,
                template_chain_id=template_chain_id,
                query_chain_id=query_chain_id,
                disulfide_distance_threshold=disulfide_distance_threshold,
                distance_buffer=distance_buffer,
                base_weight=base_weight,
                sequence_identity_weight=sequence_identity_weight,
                constraint_type=constraint_type
            )
            
            constraint_type_str = constraint_type.replace("_", " ").title()
            print(f"  INFO: Generated {len(constraints)} disulfide {constraint_type_str} constraints")
            return constraints
            
        except Exception as e:
            # A3 answer: Warning and continue
            warnings.warn(f"Failed to generate disulfide constraints: {e}")
            return []


def apply_template_constraints(
    schema_data: Dict[str, Any],
    template_structure: str,
    template_chain_id: str,
    target_chain_id: str = "A",
    constraint_type: str = "nmr_distance",
    distance_buffer: float = 0.1,
    base_weight: float = 1.0,
    sequence_identity_weight: bool = True,
    include_disulfide: bool = True,
    disulfide_distance_threshold: float = 2.2,
    **kwargs
) -> Dict[str, Any]:
    """
    Convenience function to apply template constraints to schema.
    
    Parameters
    ----------
    schema_data : Dict[str, Any]
        Boltz schema data
    template_structure : str
        Path to template structure
    template_chain_id : str
        Template chain ID
    target_chain_id : str
        Target chain ID
    constraint_type : str
        Constraint type
    distance_buffer : float
        Distance buffer for NMR constraints
    base_weight : float
        Base weight for constraints
    sequence_identity_weight : bool
        Whether to use sequence identity weighting
    include_disulfide : bool, default=True
        Whether to include disulfide bond constraints
    disulfide_distance_threshold : float, default=2.2
        Maximum distance (Angstroms) to consider as disulfide bond
        
    Returns
    -------
    Dict[str, Any]
        Updated schema data
    """
    # Find target sequence
    target_sequence = None
    for item in schema_data.get("sequences", []):
        entity_type = next(iter(item.keys())).lower()
        if entity_type == "protein":
            chain_ids = item[entity_type]["id"]
            if isinstance(chain_ids, str):
                chain_ids = [chain_ids]
            if target_chain_id in chain_ids:
                target_sequence = item[entity_type]["sequence"]
                break
    
    if not target_sequence:
        warnings.warn(f"Could not find sequence for chain {target_chain_id}")
        return schema_data
    
    # Generate constraints
    generator = TemplateConstraintGenerator(
        include_disulfide=include_disulfide,
        disulfide_distance_threshold=disulfide_distance_threshold,
        **kwargs
    )
    template_info = {
        "query_sequence": target_sequence,
        "template_structure": template_structure,
        "template_chain_id": template_chain_id,
        "query_chain_id": target_chain_id,
        "constraint_type": constraint_type,
        "distance_buffer": distance_buffer,
        "base_weight": base_weight,
        "sequence_identity_weight": sequence_identity_weight,
        "include_disulfide": include_disulfide
    }
    
    return generator.generate_constraints_for_boltz_schema(schema_data, template_info)
"""
Interface for Amber tleap program to generate topology files.
"""

import os
import subprocess
import tempfile
from pathlib import Path
from typing import List, Optional, Tuple
from datetime import datetime
import warnings

from .types import AmberForceFieldParams


class TleapInterface:
    """
    Interface to Amber tleap for topology generation.
    
    This class provides methods to:
    - Generate topology files from sequences
    - Handle different force fields
    - Create solvated systems
    """
    
    def __init__(
        self,
        amber_home: Optional[str] = None,
        force_field_params: Optional[AmberForceFieldParams] = None
    ):
        """
        Initialize tleap interface.
        
        Parameters
        ----------
        amber_home : Optional[str]
            Path to Amber installation. If None, uses AMBERHOME environment variable
        force_field_params : Optional[AmberForceFieldParams]
            Force field parameters. If None, uses defaults
        """
        self.amber_home = amber_home or os.environ.get("AMBERHOME")
        if not self.amber_home:
            raise ValueError("AMBERHOME not set. Please set AMBERHOME environment variable or provide amber_home parameter")
        
        self.tleap_path = Path(self.amber_home) / "bin" / "tleap"
        if not self.tleap_path.exists():
            raise FileNotFoundError(f"tleap not found at {self.tleap_path}")
        
        self.force_field_params = force_field_params or AmberForceFieldParams()
    
    def _create_amber_output_dir(self, base_name: str = "amber") -> Path:
        """
        Create Amber output directory in Boltz-compatible location.
        
        Parameters
        ----------
        base_name : str
            Base name for the amber output directory
            
        Returns
        -------
        Path
            Path to the created amber output directory
        """
        # Check for BOLTZ_OUTPUT_DIR environment variable
        boltz_output_env = os.environ.get("BOLTZ_OUTPUT_DIR")
        if boltz_output_env:
            boltz_output_dir = Path(boltz_output_env)
            if boltz_output_dir.exists():
                amber_dir = boltz_output_dir / "processed" / base_name
                amber_dir.mkdir(parents=True, exist_ok=True)
                print(f"  INFO: Using BOLTZ_OUTPUT_DIR environment variable")
                print(f"  INFO: Amber files will be saved to: {amber_dir}")
                return amber_dir
        
        # Look for boltz_results_* directories in current working directory
        cwd = Path.cwd()
        boltz_results_dirs = list(cwd.glob("boltz_results_*"))
        
        if boltz_results_dirs:
            # Use the most recent boltz_results directory
            latest_boltz_dir = max(boltz_results_dirs, key=lambda p: p.stat().st_mtime)
            amber_dir = latest_boltz_dir / "processed" / base_name
            amber_dir.mkdir(parents=True, exist_ok=True)
            print(f"  INFO: Found Boltz output directory: {latest_boltz_dir}")
            print(f"  INFO: Amber files will be saved to: {amber_dir}")
            return amber_dir
        
        # Fallback: create in outputs directory with timestamp
        outputs_dir = cwd / "outputs"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        amber_dir = outputs_dir / f"amber_output_{timestamp}"
        amber_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"  INFO: No Boltz output directory found, using fallback location")
        print(f"  INFO: Amber files will be saved to: {amber_dir}")
        return amber_dir
    
    def sequence_to_tleap_format(self, sequence: str) -> str:
        """
        Convert amino acid sequence to tleap sequence format.
        
        Parameters
        ----------
        sequence : str
            Amino acid sequence (one letter code)
            
        Returns
        -------
        str
            Tleap sequence format string
        """
        # Three letter code mapping
        aa_3letter = {
            'A': 'ALA', 'C': 'CYS', 'D': 'ASP', 'E': 'GLU', 'F': 'PHE', 'G': 'GLY', 'H': 'HIS', 'I': 'ILE',
            'K': 'LYS', 'L': 'LEU', 'M': 'MET', 'N': 'ASN', 'P': 'PRO', 'Q': 'GLN', 'R': 'ARG', 'S': 'SER',
            'T': 'THR', 'V': 'VAL', 'W': 'TRP', 'Y': 'TYR'
        }
        
        # Convert sequence to three letter codes
        residues = []
        
        # Convert each amino acid
        for aa in sequence:
            if aa not in aa_3letter:
                warnings.warn(f"Unknown amino acid: {aa}, using ALA as placeholder")
                residues.append('ALA')
            else:
                residues.append(aa_3letter[aa])
        
        # Format for tleap sequence command
        return ' '.join(residues)
    
    def generate_topology(
        self,
        sequence: str,
        output_prefix: str,
        chain_id: str = "A",
        solvate: bool = False,
        working_dir: Optional[Path] = None
    ) -> Tuple[Path, Path]:
        """
        Generate Amber topology and coordinate files from sequence.
        
        Parameters
        ----------
        sequence : str
            Amino acid sequence
        output_prefix : str
            Prefix for output files
        chain_id : str
            Chain identifier (not used in sequence method but kept for compatibility)
        solvate : bool
            Whether to solvate the system
        working_dir : Optional[Path]
            Working directory. If None, creates amber_output directory in current working directory
            
        Returns
        -------
        Tuple[Path, Path]
            Paths to topology (.prmtop) and coordinate (.inpcrd) files
        """
        # Create working directory
        if working_dir is None:
            working_dir = self._create_amber_output_dir()
        else:
            working_dir = Path(working_dir)
            working_dir.mkdir(parents=True, exist_ok=True)
            print(f"  INFO: Using provided working directory: {working_dir}")
        
        # Convert sequence to tleap format
        tleap_sequence = self.sequence_to_tleap_format(sequence)
        
        # Generate tleap script
        tleap_script = self._generate_tleap_script(
            sequence=tleap_sequence,
            output_prefix=output_prefix,
            solvate=solvate
        )
        
        script_path = working_dir / f"{output_prefix}_tleap.in"
        with open(script_path, 'w') as f:
            f.write(tleap_script)
        
        # Run tleap
        self._run_tleap(script_path, working_dir)
        
        # Check output files
        prmtop_path = working_dir / f"{output_prefix}.prmtop"
        inpcrd_path = working_dir / f"{output_prefix}.inpcrd"
        
        if not prmtop_path.exists() or not inpcrd_path.exists():
            raise RuntimeError(f"Failed to generate topology files. Check tleap output in {working_dir}")
        
        return prmtop_path, inpcrd_path
    
    def _generate_tleap_script(
        self,
        sequence: str,
        output_prefix: str,
        solvate: bool = False
    ) -> str:
        """
        Generate tleap input script using sequence command.
        
        Parameters
        ----------
        sequence : str
            Tleap format sequence string (e.g., "ALA CYS ASP GLU")
        output_prefix : str
            Prefix for output files
        solvate : bool
            Whether to solvate the system
            
        Returns
        -------
        str
            Tleap script content
        """
        lines = []
        
        # Load force field
        lines.extend(self.force_field_params.get_tleap_commands())
        
        # Create molecule from sequence
        # Split long sequences into multiple lines for tleap compatibility
        seq_parts = sequence.split()
        if len(seq_parts) > 50:  # Split if more than 50 residues
            lines.append("mol = sequence {")
            # Add residues in chunks of 20 per line
            for i in range(0, len(seq_parts), 20):
                chunk = " ".join(seq_parts[i:i+20])
                lines.append(f"  {chunk}")
            lines.append("}")
        else:
            lines.append(f"mol = sequence {{ {sequence} }}")
        #print(f"  INFO: Tleap sequence: {lines}")
        
        # Check structure
        lines.append("check mol")
        
        # Solvate if requested
        if solvate:
            lines.append(f"solvatebox mol {self.force_field_params.water_model}BOX {self.force_field_params.box_padding}")
            
            if self.force_field_params.neutralize:
                lines.append("addions mol Na+ 0")
                lines.append("addions mol Cl- 0")
        
        # Save topology and coordinates
        lines.append(f"saveamberparm mol {output_prefix}.prmtop {output_prefix}.inpcrd")
        lines.append("quit")
        
        return "\n".join(lines)
    
    def _run_tleap(self, script_path: Path, working_dir: Path) -> None:
        """Run tleap with the given script."""
        log_path = working_dir / "tleap.log"
        
        # Use absolute path for script
        script_path = script_path.absolute()
        print(f"  INFO: Running tleap with script: {script_path}")
        with open(log_path, 'w') as log_file:
            result = subprocess.run(
                [str(self.tleap_path), "-f", str(script_path)],
                cwd=working_dir,
                stdout=log_file,
                stderr=subprocess.STDOUT,
                text=True
            )
        
        if result.returncode != 0:
            with open(log_path, 'r') as f:
                log_content = f.read()
            raise RuntimeError(f"tleap failed with return code {result.returncode}. Log:\n{log_content}") 
"""
Parser for Amber cpptraj output to extract topology information.
"""

import os
import re
import subprocess
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import numpy as np

from .types import (
    AmberAtom,
    AmberBond,
    AmberAngle,
    AmberDihedral,
    AmberTopology
)


class CpptrajParser:
    """
    Parser for extracting topology information using cpptraj.
    
    This class provides methods to:
    - Extract atom information (atominfo)
    - Extract bond parameters (bondinfo)
    - Extract angle parameters (angleinfo)
    - Extract dihedral parameters (dihedralinfo)
    - Extract improper parameters (improperinfo)
    """
    
    def __init__(self, amber_home: Optional[str] = None, working_dir: Optional[Path] = None):
        """
        Initialize cpptraj parser.
        
        Parameters
        ----------
        amber_home : Optional[str]
            Path to Amber installation. If None, uses AMBERHOME environment variable
        working_dir : Optional[Path]
            Directory to save cpptraj output files. If None, output is not saved to files
        """
        self.amber_home = amber_home or os.environ.get("AMBERHOME")
        if not self.amber_home:
            raise ValueError("AMBERHOME not set")
        
        self.cpptraj_path = Path(self.amber_home) / "bin" / "cpptraj"
        if not self.cpptraj_path.exists():
            raise FileNotFoundError(f"cpptraj not found at {self.cpptraj_path}")
        
        self.working_dir = Path(working_dir) if working_dir else None
        if self.working_dir:
            self.working_dir.mkdir(parents=True, exist_ok=True)
    
    def parse_topology(self, prmtop_path: Path) -> AmberTopology:
        """
        Parse Amber topology file to extract all parameters.
        
        Parameters
        ----------
        prmtop_path : Path
            Path to Amber topology file (.prmtop)
            
        Returns
        -------
        AmberTopology
            Complete topology information
        """
        # Extract different components
        atoms = self._extract_atoms(prmtop_path)
        bonds = self._extract_bonds(prmtop_path, atoms)
        angles = self._extract_angles(prmtop_path, atoms)
        dihedrals = self._extract_dihedrals(prmtop_path, atoms)
        box_dims = self._extract_box_dimensions(prmtop_path)
        
        return AmberTopology(
            atoms=atoms,
            bonds=bonds,
            angles=angles,
            dihedrals=dihedrals,
            box_dimensions=box_dims
        )
    
    def _run_cpptraj_command(self, prmtop_path: Path, commands: List[str], output_prefix: str = "cpptraj") -> str:
        """
        Run cpptraj with given commands and return output.
        
        Parameters
        ----------
        prmtop_path : Path
            Path to topology file
        commands : List[str]
            List of cpptraj commands
        output_prefix : str
            Prefix for output file name when saving to disk
            
        Returns
        -------
        str
            cpptraj output
        """
        # Create cpptraj input
        cpptraj_input = f"parm {prmtop_path}\n"
        cpptraj_input += "\n".join(commands)
        cpptraj_input += "\nquit\n"
        
        # Run cpptraj
        result = subprocess.run(
            [str(self.cpptraj_path)],
            input=cpptraj_input,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            raise RuntimeError(f"cpptraj failed: {result.stderr}")
        
        # Save output to file if working_dir is specified
        if self.working_dir:
            # Determine command type from first command for filename
            command_type = "general"
            if commands:
                first_cmd = commands[0].strip().lower()
                if "atominfo" in first_cmd:
                    command_type = "atominfo"
                elif "bondinfo" in first_cmd:
                    command_type = "bondinfo"
                elif "angleinfo" in first_cmd:
                    command_type = "angleinfo"
                elif "dihedralinfo" in first_cmd:
                    command_type = "dihedralinfo"
                elif "box" in first_cmd:
                    command_type = "box"
            
            # Create output filename
            output_filename = f"{output_prefix}_{command_type}.out"
            output_path = self.working_dir / output_filename
            
            try:
                with open(output_path, 'w') as f:
                    # Write cpptraj input commands as header
                    f.write("# Cpptraj input commands:\n")
                    f.write(f"# parm {prmtop_path}\n")
                    for cmd in commands:
                        f.write(f"# {cmd}\n")
                    f.write("# quit\n")
                    f.write("\n# Cpptraj output:\n")
                    
                    # Write the actual output
                    f.write(result.stdout)
                
                print(f"  INFO: Cpptraj {command_type} output saved to: {output_path}")
            except Exception as e:
                print(f"  WARNING: Failed to save cpptraj output to file: {e}")
        
        return result.stdout
    
    def _extract_atoms(self, prmtop_path: Path) -> List[AmberAtom]:
        """Extract atom information from topology."""
        # Use atominfo command to get detailed atom information
        commands = ["atominfo @*"]
        # Extract prefix from topology filename (e.g., "chain_A" from "chain_A.prmtop")
        output_prefix = prmtop_path.stem
        output = self._run_cpptraj_command(prmtop_path, commands, output_prefix)
        
        atoms = []
        lines = output.split('\n')
        
        # Look for atom information in output
        in_atom_section = False
        atom_idx = 0
        
        for line in lines:
            # Check for various header patterns
            if any(header in line for header in ["#Atom", "Atom #", "ATOM"]):
                in_atom_section = True
                continue
            elif in_atom_section and (line.strip() == "" or "TIME:" in line or "quit" in line):
                break
            elif in_atom_section and line.strip() and not line.startswith('#'):
                # Parse atom info line
                # Typical format: AtomNum AtomName ResNum ResName MolNum AtomType Charge Mass
                parts = line.split()
                if len(parts) >= 6:
                    try:
                        # Extract fields based on typical cpptraj atominfo output
                        atom_num = int(parts[0]) - 1  # Convert to 0-based
                        atom_name = parts[1]
                        res_num = int(parts[2]) - 1  # Convert to 0-based
                        res_name = parts[3]
                        atom_type = parts[5] if len(parts) > 5 else "UNK"
                        charge = float(parts[6]) if len(parts) > 6 else 0.0
                        mass = float(parts[7]) if len(parts) > 7 else 1.0
                        
                        atom = AmberAtom(
                            index=atom_idx,
                            name=atom_name,
                            type=atom_type,
                            charge=charge,
                            mass=mass,
                            residue_index=res_num,
                            residue_name=res_name
                        )
                        atoms.append(atom)
                        atom_idx += 1
                    except (ValueError, IndexError) as e:
                        # Try alternative parsing if standard format fails
                        continue
        
        # If no atoms found, try alternative command
        if not atoms:
            # Try with resinfo for basic structure info
            commands = ["resinfo", "print atoms"]
            output = self._run_cpptraj_command(prmtop_path, commands, output_prefix)
            
            # Extract basic counts and create placeholder atoms
            n_atoms = 0
            for line in output.split('\n'):
                if "atoms" in line.lower():
                    numbers = re.findall(r'\d+', line)
                    if numbers:
                        n_atoms = int(numbers[0])
                        break
            
            # Create basic atom placeholders
            for i in range(n_atoms):
                atom = AmberAtom(
                    index=i,
                    name=f"AT{i+1}",
                    type="UNK",
                    charge=0.0,
                    mass=1.0,
                    residue_index=i // 20,  # Rough estimate: 20 atoms per residue
                    residue_name="UNK"
                )
                atoms.append(atom)
        
        return atoms
    
    def _extract_bonds(self, prmtop_path: Path, atoms: List[AmberAtom]) -> List[AmberBond]:
        """Extract bond information from topology."""
        bonds = []
        
        # Use bondinfo command to get bond parameters
        commands = ["bondinfo"]
        # Extract prefix from topology filename
        output_prefix = prmtop_path.stem
        try:
            output = self._run_cpptraj_command(prmtop_path, commands, output_prefix)
            
            lines = output.split('\n')
            in_bond_section = False
            
            for line in lines:
                # Look for bond information section header
                if "#Bnd" in line and "RK" in line and "REQ" in line:
                    in_bond_section = True
                    continue
                elif in_bond_section and (line.strip() == "" or "TIME:" in line or "quit" in line):
                    break
                elif in_bond_section and line.strip() and not line.startswith('#'):
                    # Parse bond line
                    # Format: BondNum RK REQ Atom1 Atom2 A1 A2 T1 T2
                    # Example:    1 340.00  1.090 :1@CB   :1@HB1   5  6 CT HC
                    parts = line.split()
                    if len(parts) >= 7:
                        try:
                            # Extract force constant and equilibrium distance
                            force_constant = float(parts[1])  # RK
                            equilibrium_distance = float(parts[2])  # REQ
                            
                            # Extract atom indices from A1 A2 columns (convert to 0-based)
                            atom1_idx = int(parts[5]) - 1  # A1
                            atom2_idx = int(parts[6]) - 1  # A2
                            
                            # Validate atom indices
                            if 0 <= atom1_idx < len(atoms) and 0 <= atom2_idx < len(atoms):
                                bond = AmberBond(
                                    atom1_idx=atom1_idx,
                                    atom2_idx=atom2_idx,
                                    force_constant=force_constant,
                                    equilibrium_distance=equilibrium_distance
                                )
                                bonds.append(bond)
                        except (ValueError, IndexError):
                            continue
            
            # If no bonds extracted from bondinfo, try fallback method
            if not bonds:
                self._generate_fallback_bonds(atoms, bonds)
                                
        except Exception as e:
            # If bondinfo fails, generate fallback bonds based on atom names
            self._generate_fallback_bonds(atoms, bonds)
        
        return bonds
    
    def _extract_angles(self, prmtop_path: Path, atoms: List[AmberAtom]) -> List[AmberAngle]:
        """Extract angle information from topology."""
        angles = []
        
        # Use angleinfo command to get angle parameters
        commands = ["angleinfo"]
        # Extract prefix from topology filename
        output_prefix = prmtop_path.stem
        try:
            output = self._run_cpptraj_command(prmtop_path, commands, output_prefix)
            
            lines = output.split('\n')
            in_angle_section = False
            
            for line in lines:
                # Look for angle information section header
                if "#Ang" in line and "TK" in line and "TEQ" in line:
                    in_angle_section = True
                    continue
                elif in_angle_section and (line.strip() == "" or "TIME:" in line or "quit" in line):
                    break
                elif in_angle_section and line.strip() and not line.startswith('#'):
                    # Parse angle line
                    # Format: AngNum TK TEQ Atom1 Atom2 Atom3 A1 A2 A3 T1 T2 T3
                    # Example:    1 50.000 120.00 :1@C    :2@N    :2@H     9 11 12  C  N  H
                    parts = line.split()
                    if len(parts) >= 9:
                        try:
                            # Extract force constant and equilibrium angle
                            force_constant = float(parts[1])  # TK
                            equilibrium_angle = float(parts[2])  # TEQ
                            
                            # Extract atom indices from A1 A2 A3 columns (convert to 0-based)
                            atom1_idx = int(parts[6]) - 1  # A1
                            atom2_idx = int(parts[7]) - 1  # A2
                            atom3_idx = int(parts[8]) - 1  # A3
                            
                            # Convert angle to radians if in degrees
                            if equilibrium_angle > 6.28:  # Likely in degrees
                                equilibrium_angle = np.radians(equilibrium_angle)
                            
                            # Validate atom indices
                            if (0 <= atom1_idx < len(atoms) and 
                                0 <= atom2_idx < len(atoms) and 
                                0 <= atom3_idx < len(atoms)):
                                
                                angle = AmberAngle(
                                    atom1_idx=atom1_idx,
                                    atom2_idx=atom2_idx,
                                    atom3_idx=atom3_idx,
                                    force_constant=force_constant,
                                    equilibrium_angle=equilibrium_angle
                                )
                                angles.append(angle)
                        except (ValueError, IndexError):
                            continue
            
            # If no angles extracted, use fallback method
            if not angles:
                self._generate_fallback_angles(atoms, angles)
                                
        except Exception as e:
            # If angleinfo fails, generate fallback angles
            self._generate_fallback_angles(atoms, angles)
        
        return angles
    
    def _extract_dihedrals(self, prmtop_path: Path, atoms: List[AmberAtom]) -> List[AmberDihedral]:
        """Extract dihedral information from topology."""
        dihedrals = []
        
        # Use dihedralinfo command to get dihedral parameters
        commands = ["dihedralinfo"]
        # Extract prefix from topology filename
        output_prefix = prmtop_path.stem
        try:
            output = self._run_cpptraj_command(prmtop_path, commands, output_prefix)
            
            lines = output.split('\n')
            in_dihedral_section = False
            
            for line in lines:
                # Look for dihedral information section header
                if "# Dih" in line and "PK" in line and "Phase" in line and "PN" in line:
                    in_dihedral_section = True
                    continue
                elif in_dihedral_section and (line.strip() == "" or "TIME:" in line or "quit" in line):
                    break
                elif in_dihedral_section and line.strip() and not line.startswith('#'):
                    # Parse dihedral line
                    # Format: DihedralNum PK Phase PN Atom1 Atom2 Atom3 Atom4 A1 A2 A3 A4 T1 T2 T3 T4
                    # Example:     1   2.000  0.00  1.0 :1@O    :1@C    :2@N    :2@H    10  9 11 12  O  C  N  H
                    # Note: Some lines may start with 'E' (improper)
                    
                    # Remove leading 'E' if present (improper dihedral)
                    clean_line = line.lstrip('E').strip()
                    parts = clean_line.split()
                    
                    if len(parts) >= 12:
                        try:
                            # Extract force constant, phase, and periodicity
                            force_constant = float(parts[1])  # PK
                            phase = float(parts[2])  # Phase
                            periodicity = int(float(parts[3]))  # PN (convert to int)
                            
                            # Extract atom indices from A1 A2 A3 A4 columns (convert to 0-based)
                            atom1_idx = int(parts[8]) - 1   # A1
                            atom2_idx = int(parts[9]) - 1   # A2
                            atom3_idx = int(parts[10]) - 1  # A3
                            atom4_idx = int(parts[11]) - 1  # A4
                            
                            # Convert phase to radians if in degrees
                            if abs(phase) > 6.28:  # Likely in degrees
                                phase = np.radians(phase)
                            
                            # Validate atom indices
                            if (0 <= atom1_idx < len(atoms) and 
                                0 <= atom2_idx < len(atoms) and 
                                0 <= atom3_idx < len(atoms) and 
                                0 <= atom4_idx < len(atoms)):
                                
                                dihedral = AmberDihedral(
                                    atom1_idx=atom1_idx,
                                    atom2_idx=atom2_idx,
                                    atom3_idx=atom3_idx,
                                    atom4_idx=atom4_idx,
                                    force_constant=force_constant,
                                    phase=phase,
                                    periodicity=periodicity
                                )
                                dihedrals.append(dihedral)
                        except (ValueError, IndexError):
                            continue
            
            # If no dihedrals extracted, use fallback method
            if not dihedrals:
                self._generate_fallback_dihedrals(atoms, dihedrals)
                                
        except Exception as e:
            # If dihedralinfo fails, generate fallback dihedrals
            self._generate_fallback_dihedrals(atoms, dihedrals)
        
        return dihedrals
    
    def _get_default_bond_params(self, atom1_name: str, atom2_name: str, residue_name: str) -> Tuple[float, float]:
        """Get default bond parameters for common atom pairs including side chains."""
        # Default force field parameters for common bonds (force_constant, equilibrium_distance)
        bond_params = {
            # Backbone bonds
            ("N", "CA"): (434.0, 1.449),   # kcal/mol/A^2, Angstroms
            ("CA", "C"): (469.0, 1.522),
            ("C", "O"): (570.0, 1.229),
            ("C", "N"): (490.0, 1.335),
            ("N", "H"): (434.0, 1.010),
            ("CA", "HA"): (367.0, 1.090),
            
            # Side chain bonds - common patterns
            ("CA", "CB"): (367.0, 1.526),   # CA-CB (universal)
            ("CB", "CG"): (367.0, 1.520),   # CB-CG (most amino acids)
            ("CG", "CD"): (367.0, 1.520),   # CG-CD (long chains)
            ("CD", "CE"): (367.0, 1.520),   # CD-CE (ARG, LYS)
            ("CE", "NZ"): (367.0, 1.489),   # CE-NZ (LYS)
            ("CZ", "NH1"): (490.0, 1.340),  # CZ-NH1 (ARG)
            ("CZ", "NH2"): (490.0, 1.340),  # CZ-NH2 (ARG)
            
            # Side chain hydrogen bonds
            ("CB", "HB"): (367.0, 1.090),   # CB-H bonds
            ("CG", "HG"): (367.0, 1.090),   # CG-H bonds
            ("CD", "HD"): (367.0, 1.090),   # CD-H bonds
            ("CE", "HE"): (367.0, 1.090),   # CE-H bonds
            
            # Aromatic bonds (PHE, TYR, TRP, HIS)
            ("CG", "CD1"): (469.0, 1.400),  # Aromatic C-C
            ("CG", "CD2"): (469.0, 1.400),  # Aromatic C-C
            ("CD1", "CE1"): (469.0, 1.400), # Aromatic C-C
            ("CD2", "CE2"): (469.0, 1.400), # Aromatic C-C
            ("CE1", "CZ"): (469.0, 1.400),  # Aromatic C-C
            ("CE2", "CZ"): (469.0, 1.400),  # Aromatic C-C
            ("CD1", "HD1"): (367.0, 1.080), # Aromatic C-H
            ("CD2", "HD2"): (367.0, 1.080), # Aromatic C-H
            ("CE1", "HE1"): (367.0, 1.080), # Aromatic C-H
            ("CE2", "HE2"): (367.0, 1.080), # Aromatic C-H
            ("CZ", "HZ"): (367.0, 1.080),   # Aromatic C-H
            
            # Hydroxyl groups (SER, THR, TYR)
            ("CB", "OG"): (320.0, 1.426),   # SER CB-OG
            ("CG1", "OG1"): (320.0, 1.426), # THR CG1-OG1
            ("CZ", "OH"): (320.0, 1.364),   # TYR CZ-OH
            ("OG", "HG"): (553.0, 0.960),   # O-H bonds
            ("OG1", "HG1"): (553.0, 0.960), # O-H bonds
            ("OH", "HH"): (553.0, 0.960),   # O-H bonds
            
            # Sulfur bonds (CYS, MET)
            ("CB", "SG"): (229.0, 1.810),   # CYS CB-SG
            ("SG", "HG"): (274.0, 1.336),   # CYS SG-HG
            ("CG", "SD"): (229.0, 1.810),   # MET CG-SD
            ("SD", "CE"): (229.0, 1.810),   # MET SD-CE
            
            # Carboxyl groups (ASP, GLU)
            ("CG", "OD1"): (570.0, 1.229),  # ASP CG-OD1
            ("CG", "OD2"): (570.0, 1.229),  # ASP CG-OD2
            ("CD", "OE1"): (570.0, 1.229),  # GLU CD-OE1
            ("CD", "OE2"): (570.0, 1.229),  # GLU CD-OE2
            
            # Amide groups (ASN, GLN)
            ("CG", "ND2"): (490.0, 1.335),  # ASN CG-ND2
            ("CD", "NE2"): (490.0, 1.335),  # GLN CD-NE2
            ("CG", "OD1"): (570.0, 1.229),  # ASN CG-OD1
            ("CD", "OE1"): (570.0, 1.229),  # GLN CD-OE1
            ("ND2", "HD21"): (434.0, 1.010), # Amide N-H
            ("ND2", "HD22"): (434.0, 1.010), # Amide N-H
            ("NE2", "HE21"): (434.0, 1.010), # Amide N-H
            ("NE2", "HE22"): (434.0, 1.010), # Amide N-H
            
            # Proline ring (PRO)
            ("CB", "CG"): (367.0, 1.526),   # PRO CB-CG
            ("CG", "CD"): (367.0, 1.526),   # PRO CG-CD
            ("CD", "N"): (367.0, 1.466),    # PRO CD-N (ring closure)
            
            # Histidine (HIS)
            ("CD2", "NE2"): (410.0, 1.374), # HIS aromatic C-N
            ("CE1", "NE2"): (488.0, 1.320), # HIS aromatic C-N
            ("CG", "ND1"): (410.0, 1.374),  # HIS aromatic C-N
            ("CD2", "HD2"): (367.0, 1.080), # HIS aromatic C-H
            ("CE1", "HE1"): (367.0, 1.080), # HIS aromatic C-H
            ("ND1", "HD1"): (434.0, 1.010), # HIS N-H
            ("NE2", "HE2"): (434.0, 1.010), # HIS N-H
            
            # Tryptophan (TRP)
            ("CD1", "NE1"): (410.0, 1.374), # TRP aromatic C-N
            ("CE2", "NE1"): (410.0, 1.374), # TRP aromatic C-N
            ("CD2", "CE3"): (469.0, 1.400), # TRP aromatic C-C
            ("CE3", "CZ3"): (469.0, 1.400), # TRP aromatic C-C
            ("CZ3", "CH2"): (469.0, 1.400), # TRP aromatic C-C
            ("CH2", "CZ2"): (469.0, 1.400), # TRP aromatic C-C
            ("CZ2", "CE2"): (469.0, 1.400), # TRP aromatic C-C
            ("NE1", "HE1"): (434.0, 1.010), # TRP N-H
        }
        
        # Try both orders for symmetric bonds
        key1 = (atom1_name, atom2_name)
        key2 = (atom2_name, atom1_name)
        
        if key1 in bond_params:
            return bond_params[key1]
        elif key2 in bond_params:
            return bond_params[key2]
        else:
            # Default values for unknown bonds
            # Use atom name patterns for better defaults
            if "H" in atom1_name or "H" in atom2_name:
                return (350.0, 1.090)  # X-H bonds
            elif "O" in atom1_name or "O" in atom2_name:
                return (320.0, 1.400)  # X-O bonds
            elif "N" in atom1_name or "N" in atom2_name:
                return (367.0, 1.450)  # X-N bonds
            elif "S" in atom1_name or "S" in atom2_name:
                return (229.0, 1.810)  # X-S bonds
            else:
                return (300.0, 1.520)  # C-C bonds (default)
    
    def _get_sidechain_bonds(self, residue_name: str) -> List[Tuple[str, str]]:
        """Get standard side chain bond connectivity for amino acids."""
        # Define side chain bond connectivity for each amino acid
        sidechain_bonds = {
            "ALA": [("CA", "CB"), ("CB", "HB1"), ("CB", "HB2"), ("CB", "HB3")],
            "VAL": [("CA", "CB"), ("CB", "CG1"), ("CB", "CG2"), ("CB", "HB"),
                   ("CG1", "HG11"), ("CG1", "HG12"), ("CG1", "HG13"),
                   ("CG2", "HG21"), ("CG2", "HG22"), ("CG2", "HG23")],
            "LEU": [("CA", "CB"), ("CB", "CG"), ("CG", "CD1"), ("CG", "CD2"),
                   ("CB", "HB2"), ("CB", "HB3"), ("CG", "HG"),
                   ("CD1", "HD11"), ("CD1", "HD12"), ("CD1", "HD13"),
                   ("CD2", "HD21"), ("CD2", "HD22"), ("CD2", "HD23")],
            "ILE": [("CA", "CB"), ("CB", "CG1"), ("CB", "CG2"), ("CG1", "CD1"),
                   ("CB", "HB"), ("CG1", "HG12"), ("CG1", "HG13"),
                   ("CG2", "HG21"), ("CG2", "HG22"), ("CG2", "HG23"),
                   ("CD1", "HD11"), ("CD1", "HD12"), ("CD1", "HD13")],
            "PHE": [("CA", "CB"), ("CB", "CG"), ("CG", "CD1"), ("CG", "CD2"),
                   ("CD1", "CE1"), ("CD2", "CE2"), ("CE1", "CZ"), ("CE2", "CZ"),
                   ("CB", "HB2"), ("CB", "HB3"), ("CD1", "HD1"), ("CD2", "HD2"),
                   ("CE1", "HE1"), ("CE2", "HE2"), ("CZ", "HZ")],
            "TYR": [("CA", "CB"), ("CB", "CG"), ("CG", "CD1"), ("CG", "CD2"),
                   ("CD1", "CE1"), ("CD2", "CE2"), ("CE1", "CZ"), ("CE2", "CZ"),
                   ("CZ", "OH"), ("CB", "HB2"), ("CB", "HB3"),
                   ("CD1", "HD1"), ("CD2", "HD2"), ("CE1", "HE1"), ("CE2", "HE2"),
                   ("OH", "HH")],
            "TRP": [("CA", "CB"), ("CB", "CG"), ("CG", "CD1"), ("CG", "CD2"),
                   ("CD1", "NE1"), ("CD2", "CE2"), ("CE2", "NE1"), ("CD2", "CE3"),
                   ("CE2", "CZ2"), ("CE3", "CZ3"), ("CZ2", "CH2"), ("CZ3", "CH2"),
                   ("CB", "HB2"), ("CB", "HB3"), ("CD1", "HD1"), ("NE1", "HE1"),
                   ("CE3", "HE3"), ("CZ2", "HZ2"), ("CZ3", "HZ3"), ("CH2", "HH2")],
            "SER": [("CA", "CB"), ("CB", "OG"), ("CB", "HB2"), ("CB", "HB3"), ("OG", "HG")],
            "THR": [("CA", "CB"), ("CB", "OG1"), ("CB", "CG2"), ("CB", "HB"),
                   ("CG2", "HG21"), ("CG2", "HG22"), ("CG2", "HG23"), ("OG1", "HG1")],
            "CYS": [("CA", "CB"), ("CB", "SG"), ("CB", "HB2"), ("CB", "HB3"), ("SG", "HG")],
            "MET": [("CA", "CB"), ("CB", "CG"), ("CG", "SD"), ("SD", "CE"),
                   ("CB", "HB2"), ("CB", "HB3"), ("CG", "HG2"), ("CG", "HG3"),
                   ("CE", "HE1"), ("CE", "HE2"), ("CE", "HE3")],
            "ASP": [("CA", "CB"), ("CB", "CG"), ("CG", "OD1"), ("CG", "OD2"),
                   ("CB", "HB2"), ("CB", "HB3")],
            "GLU": [("CA", "CB"), ("CB", "CG"), ("CG", "CD"), ("CD", "OE1"), ("CD", "OE2"),
                   ("CB", "HB2"), ("CB", "HB3"), ("CG", "HG2"), ("CG", "HG3")],
            "ASN": [("CA", "CB"), ("CB", "CG"), ("CG", "OD1"), ("CG", "ND2"),
                   ("CB", "HB2"), ("CB", "HB3"), ("ND2", "HD21"), ("ND2", "HD22")],
            "GLN": [("CA", "CB"), ("CB", "CG"), ("CG", "CD"), ("CD", "OE1"), ("CD", "NE2"),
                   ("CB", "HB2"), ("CB", "HB3"), ("CG", "HG2"), ("CG", "HG3"),
                   ("NE2", "HE21"), ("NE2", "HE22")],
            "LYS": [("CA", "CB"), ("CB", "CG"), ("CG", "CD"), ("CD", "CE"), ("CE", "NZ"),
                   ("CB", "HB2"), ("CB", "HB3"), ("CG", "HG2"), ("CG", "HG3"),
                   ("CD", "HD2"), ("CD", "HD3"), ("CE", "HE2"), ("CE", "HE3"),
                   ("NZ", "HZ1"), ("NZ", "HZ2"), ("NZ", "HZ3")],
            "ARG": [("CA", "CB"), ("CB", "CG"), ("CG", "CD"), ("CD", "NE"), ("NE", "CZ"),
                   ("CZ", "NH1"), ("CZ", "NH2"), ("CB", "HB2"), ("CB", "HB3"),
                   ("CG", "HG2"), ("CG", "HG3"), ("CD", "HD2"), ("CD", "HD3"),
                   ("NE", "HE"), ("NH1", "HH11"), ("NH1", "HH12"),
                   ("NH2", "HH21"), ("NH2", "HH22")],
            "HIS": [("CA", "CB"), ("CB", "CG"), ("CG", "ND1"), ("CG", "CD2"),
                   ("ND1", "CE1"), ("CD2", "NE2"), ("CE1", "NE2"),
                   ("CB", "HB2"), ("CB", "HB3"), ("ND1", "HD1"), ("CD2", "HD2"),
                   ("CE1", "HE1"), ("NE2", "HE2")],
            "PRO": [("CA", "CB"), ("CB", "CG"), ("CG", "CD"), ("CD", "N"),
                   ("CB", "HB2"), ("CB", "HB3"), ("CG", "HG2"), ("CG", "HG3"),
                   ("CD", "HD2"), ("CD", "HD3")],
            "GLY": [],  # No side chain
        }
        
        return sidechain_bonds.get(residue_name, [])
    
    def _is_hydrogen_atom(self, atom_name: str) -> bool:
        """Check if atom is hydrogen based on atom name."""
        # Hydrogen atoms typically start with 'H' (H, HA, HB1, HG2, etc.)
        return atom_name.startswith('H')
    
    def _filter_hydrogen_bonds(self, bond_patterns: List[Tuple[str, str]]) -> List[Tuple[str, str]]:
        """Filter out bonds involving hydrogen atoms for constraint generation."""
        return [
            (atom1, atom2) for atom1, atom2 in bond_patterns 
            if not (self._is_hydrogen_atom(atom1) or self._is_hydrogen_atom(atom2))
        ]
    
    def _filter_hydrogen_angles(self, angle_patterns: List[Tuple[str, str, str, float]]) -> List[Tuple[str, str, str, float]]:
        """Filter out angles involving hydrogen atoms for constraint generation."""
        return [
            (atom1, atom2, atom3, angle) for atom1, atom2, atom3, angle in angle_patterns 
            if not (self._is_hydrogen_atom(atom1) or self._is_hydrogen_atom(atom2) or self._is_hydrogen_atom(atom3))
        ]
    
    def _filter_hydrogen_dihedrals(self, dihedral_patterns: List[Tuple[str, str, str, str, float, float, int]]) -> List[Tuple[str, str, str, str, float, float, int]]:
        """Filter out dihedrals involving hydrogen atoms for constraint generation."""
        return [
            (atom1, atom2, atom3, atom4, fc, phase, period) for atom1, atom2, atom3, atom4, fc, phase, period in dihedral_patterns 
            if not (self._is_hydrogen_atom(atom1) or self._is_hydrogen_atom(atom2) or 
                   self._is_hydrogen_atom(atom3) or self._is_hydrogen_atom(atom4))
        ]
    
    def _generate_fallback_bonds(self, atoms: List[AmberAtom], bonds: List[AmberBond]) -> None:
        """Generate backbone and side chain bonds when cpptraj parsing fails."""
        # Group atoms by residue
        residues = {}
        atom_map = {}  # Map (residue_idx, atom_name) -> atom
        
        for atom in atoms:
            if atom.residue_index not in residues:
                residues[atom.residue_index] = []
            residues[atom.residue_index].append(atom)
            atom_map[(atom.residue_index, atom.name)] = atom
        
        # Generate bonds within each residue and between residues
        for res_idx, res_atoms in residues.items():
            if not res_atoms:
                continue
                
            residue_name = res_atoms[0].residue_name
            
            # 1. Generate backbone bonds
            n_atom = atom_map.get((res_idx, "N"))
            ca_atom = atom_map.get((res_idx, "CA"))
            c_atom = atom_map.get((res_idx, "C"))
            o_atom = atom_map.get((res_idx, "O"))
            h_atom = atom_map.get((res_idx, "H"))
            ha_atom = atom_map.get((res_idx, "HA"))
            
            # Backbone bonds within residue (exclude hydrogen bonds for Boltz constraint generation)
            if n_atom and ca_atom:
                force_constant, eq_distance = self._get_default_bond_params("N", "CA", residue_name)
                bonds.append(AmberBond(n_atom.index, ca_atom.index, force_constant, eq_distance))
            
            if ca_atom and c_atom:
                force_constant, eq_distance = self._get_default_bond_params("CA", "C", residue_name)
                bonds.append(AmberBond(ca_atom.index, c_atom.index, force_constant, eq_distance))
            
            if c_atom and o_atom:
                force_constant, eq_distance = self._get_default_bond_params("C", "O", residue_name)
                bonds.append(AmberBond(c_atom.index, o_atom.index, force_constant, eq_distance))
            
            # Skip hydrogen bonds for Boltz constraint generation
            # if n_atom and h_atom:
            #     force_constant, eq_distance = self._get_default_bond_params("N", "H", residue_name)
            #     bonds.append(AmberBond(n_atom.index, h_atom.index, force_constant, eq_distance))
            
            # if ca_atom and ha_atom:
            #     force_constant, eq_distance = self._get_default_bond_params("CA", "HA", residue_name)
            #     bonds.append(AmberBond(ca_atom.index, ha_atom.index, force_constant, eq_distance))
            
            # 2. Generate side chain bonds (filter out hydrogen bonds for Boltz constraint generation)
            sidechain_bond_patterns = self._get_sidechain_bonds(residue_name)
            # Filter out hydrogen bonds for constraint generation
            non_hydrogen_sidechain_bonds = self._filter_hydrogen_bonds(sidechain_bond_patterns)
            
            for atom1_name, atom2_name in non_hydrogen_sidechain_bonds:
                atom1 = atom_map.get((res_idx, atom1_name))
                atom2 = atom_map.get((res_idx, atom2_name))
                
                if atom1 and atom2:
                    force_constant, eq_distance = self._get_default_bond_params(
                        atom1_name, atom2_name, residue_name
                    )
                    bonds.append(AmberBond(atom1.index, atom2.index, force_constant, eq_distance))
            
            # 3. Inter-residue peptide bond (C to next N)
            if c_atom and res_idx + 1 in residues:
                next_n_atom = atom_map.get((res_idx + 1, "N"))
                if next_n_atom:
                    force_constant, eq_distance = self._get_default_bond_params("C", "N", residue_name)
                    bonds.append(AmberBond(c_atom.index, next_n_atom.index, force_constant, eq_distance))
    
    def _get_sidechain_angles(self, residue_name: str) -> List[Tuple[str, str, str, float]]:
        """Get standard side chain angle patterns for amino acids."""
        # Define side chain angle connectivity (atom1, atom2, atom3, equilibrium_angle_degrees)
        sidechain_angles = {
            "ALA": [("N", "CA", "CB", 110.5), ("CA", "CB", "HB1", 109.5),
                   ("CA", "CB", "HB2", 109.5), ("CA", "CB", "HB3", 109.5)],
            "VAL": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG1", 110.5),
                   ("CA", "CB", "CG2", 110.5), ("CB", "CG1", "HG11", 109.5),
                   ("CB", "CG2", "HG21", 109.5)],
            "LEU": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 109.5),
                   ("CB", "CG", "CD1", 110.5), ("CB", "CG", "CD2", 110.5),
                   ("CG", "CD1", "HD11", 109.5), ("CG", "CD2", "HD21", 109.5)],
            "ILE": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG1", 110.5),
                   ("CA", "CB", "CG2", 110.5), ("CB", "CG1", "CD1", 109.5),
                   ("CG1", "CD1", "HD11", 109.5)],
            "PHE": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 113.8),
                   ("CB", "CG", "CD1", 120.0), ("CB", "CG", "CD2", 120.0),
                   ("CG", "CD1", "CE1", 120.0), ("CG", "CD2", "CE2", 120.0),
                   ("CD1", "CE1", "CZ", 120.0), ("CD2", "CE2", "CZ", 120.0)],
            "TYR": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 113.8),
                   ("CB", "CG", "CD1", 120.0), ("CB", "CG", "CD2", 120.0),
                   ("CG", "CD1", "CE1", 120.0), ("CG", "CD2", "CE2", 120.0),
                   ("CD1", "CE1", "CZ", 120.0), ("CD2", "CE2", "CZ", 120.0),
                   ("CE1", "CZ", "OH", 120.0), ("CE2", "CZ", "OH", 120.0)],
            "TRP": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 113.8),
                   ("CB", "CG", "CD1", 126.8), ("CB", "CG", "CD2", 108.8),
                   ("CG", "CD1", "NE1", 110.0), ("CG", "CD2", "CE2", 108.0),
                   ("CD1", "NE1", "CE2", 109.0), ("CD2", "CE2", "CZ2", 120.0)],
            "SER": [("N", "CA", "CB", 110.5), ("CA", "CB", "OG", 110.8),
                   ("CB", "OG", "HG", 106.0)],
            "THR": [("N", "CA", "CB", 110.5), ("CA", "CB", "OG1", 110.8),
                   ("CA", "CB", "CG2", 111.5), ("CB", "OG1", "HG1", 106.0)],
            "CYS": [("N", "CA", "CB", 110.5), ("CA", "CB", "SG", 114.4),
                   ("CB", "SG", "HG", 96.0)],
            "MET": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 109.5),
                   ("CB", "CG", "SD", 112.7), ("CG", "SD", "CE", 100.9)],
            "ASP": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 112.6),
                   ("CB", "CG", "OD1", 118.4), ("CB", "CG", "OD2", 118.4)],
            "GLU": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 109.5),
                   ("CB", "CG", "CD", 112.6), ("CG", "CD", "OE1", 118.4),
                   ("CG", "CD", "OE2", 118.4)],
            "ASN": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 112.6),
                   ("CB", "CG", "OD1", 121.9), ("CB", "CG", "ND2", 116.4)],
            "GLN": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 109.5),
                   ("CB", "CG", "CD", 112.6), ("CG", "CD", "OE1", 121.9),
                   ("CG", "CD", "NE2", 116.4)],
            "LYS": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 109.5),
                   ("CB", "CG", "CD", 109.5), ("CG", "CD", "CE", 109.5),
                   ("CD", "CE", "NZ", 111.7)],
            "ARG": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 109.5),
                   ("CB", "CG", "CD", 109.5), ("CG", "CD", "NE", 111.7),
                   ("CD", "NE", "CZ", 124.2), ("NE", "CZ", "NH1", 120.0),
                   ("NE", "CZ", "NH2", 120.0)],
            "HIS": [("N", "CA", "CB", 110.5), ("CA", "CB", "CG", 113.8),
                   ("CB", "CG", "ND1", 122.9), ("CB", "CG", "CD2", 130.6),
                   ("CG", "ND1", "CE1", 108.5), ("CG", "CD2", "NE2", 108.5),
                   ("ND1", "CE1", "NE2", 108.5)],
            "PRO": [("N", "CA", "CB", 103.0), ("CA", "CB", "CG", 104.5),
                   ("CB", "CG", "CD", 105.0), ("CG", "CD", "N", 103.2)],
            "GLY": [],  # No side chain
        }
        
        return sidechain_angles.get(residue_name, [])
    
    def _generate_fallback_angles(self, atoms: List[AmberAtom], angles: List[AmberAngle]) -> None:
        """Generate backbone and side chain angles when cpptraj parsing fails."""
        # Group atoms by residue
        residues = {}
        atom_map = {}  # Map (residue_idx, atom_name) -> atom
        
        for atom in atoms:
            if atom.residue_index not in residues:
                residues[atom.residue_index] = []
            residues[atom.residue_index].append(atom)
            atom_map[(atom.residue_index, atom.name)] = atom
        
        for res_idx, res_atoms in residues.items():
            if not res_atoms:
                continue
                
            residue_name = res_atoms[0].residue_name
            
            # 1. Generate backbone angles
            n_atom = atom_map.get((res_idx, "N"))
            ca_atom = atom_map.get((res_idx, "CA"))
            c_atom = atom_map.get((res_idx, "C"))
            o_atom = atom_map.get((res_idx, "O"))
            h_atom = atom_map.get((res_idx, "H"))
            
            # N-CA-C angle
            if n_atom and ca_atom and c_atom:
                angles.append(AmberAngle(
                    n_atom.index, ca_atom.index, c_atom.index,
                    80.0,  # Force constant kcal/mol/rad^2
                    np.radians(110.0)  # Equilibrium angle in radians
                ))
            
            # CA-C-O angle
            if ca_atom and c_atom and o_atom:
                angles.append(AmberAngle(
                    ca_atom.index, c_atom.index, o_atom.index,
                    80.0,  # Force constant kcal/mol/rad^2
                    np.radians(120.4)  # Equilibrium angle in radians
                ))
            
            # Skip hydrogen angles for Boltz constraint generation
            # H-N-CA angle
            # if h_atom and n_atom and ca_atom:
            #     angles.append(AmberAngle(
            #         h_atom.index, n_atom.index, ca_atom.index,
            #         50.0,  # Force constant kcal/mol/rad^2
            #         np.radians(118.0)  # Equilibrium angle in radians
            #     ))
            
            # 2. Inter-residue backbone angles
            # CA-C-N angle (with next residue)
            if ca_atom and c_atom and res_idx + 1 in residues:
                next_n_atom = atom_map.get((res_idx + 1, "N"))
                if next_n_atom:
                    angles.append(AmberAngle(
                        ca_atom.index, c_atom.index, next_n_atom.index,
                        50.0,  # Force constant
                        np.radians(116.6)  # Equilibrium angle
                    ))
            
            # C-N-CA angle (with next residue)
            if c_atom and res_idx + 1 in residues:
                next_n_atom = atom_map.get((res_idx + 1, "N"))
                next_ca_atom = atom_map.get((res_idx + 1, "CA"))
                if next_n_atom and next_ca_atom:
                    angles.append(AmberAngle(
                        c_atom.index, next_n_atom.index, next_ca_atom.index,
                        50.0,  # Force constant
                        np.radians(121.9)  # Equilibrium angle
                    ))
            
            # 3. Generate side chain angles (filter out hydrogen angles for Boltz constraint generation)
            sidechain_angle_patterns = self._get_sidechain_angles(residue_name)
            # Filter out hydrogen angles for constraint generation
            non_hydrogen_sidechain_angles = self._filter_hydrogen_angles(sidechain_angle_patterns)
            
            for atom1_name, atom2_name, atom3_name, eq_angle_deg in non_hydrogen_sidechain_angles:
                atom1 = atom_map.get((res_idx, atom1_name))
                atom2 = atom_map.get((res_idx, atom2_name))
                atom3 = atom_map.get((res_idx, atom3_name))
                
                if atom1 and atom2 and atom3:
                    # Convert degrees to radians
                    eq_angle_rad = np.radians(eq_angle_deg)
                    
                    # Use different force constants based on bond types
                    if "H" in atom1_name or "H" in atom3_name:
                        force_constant = 35.0  # Weaker for X-H bonds
                    elif any(x in atom1_name + atom3_name for x in ["O", "N", "S"]):
                        force_constant = 70.0  # Stronger for heteroatoms
                    else:
                        force_constant = 63.0  # Standard for C-C-C
                    
                    angles.append(AmberAngle(
                        atom1.index, atom2.index, atom3.index,
                        force_constant, eq_angle_rad
                    ))
    
    def _get_sidechain_dihedrals(self, residue_name: str) -> List[Tuple[str, str, str, str, float, float, int]]:
        """Get standard side chain dihedral patterns for amino acids."""
        # Define side chain dihedrals (atom1, atom2, atom3, atom4, force_constant, phase_deg, periodicity)
        sidechain_dihedrals = {
            "ALA": [],  # No rotatable side chain bonds
            "VAL": [("N", "CA", "CB", "CG1", 1.40, 60.0, 3),   # χ1
                   ("N", "CA", "CB", "CG2", 1.40, -60.0, 3)],  # χ1'
            "LEU": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1
                   ("CA", "CB", "CG", "CD1", 1.60, 60.0, 3),   # χ2
                   ("CA", "CB", "CG", "CD2", 1.60, -60.0, 3)], # χ2'
            "ILE": [("N", "CA", "CB", "CG1", 1.40, 60.0, 3),   # χ1
                   ("CA", "CB", "CG1", "CD1", 1.60, 60.0, 3)], # χ2
            "PHE": [("N", "CA", "CB", "CG", 1.40, 90.0, 2),    # χ1
                   ("CA", "CB", "CG", "CD1", 3.625, 0.0, 2)],  # χ2
            "TYR": [("N", "CA", "CB", "CG", 1.40, 90.0, 2),    # χ1
                   ("CA", "CB", "CG", "CD1", 3.625, 0.0, 2)],  # χ2
            "TRP": [("N", "CA", "CB", "CG", 1.40, 90.0, 2),    # χ1
                   ("CA", "CB", "CG", "CD1", 4.9, 0.0, 2)],    # χ2
            "SER": [("N", "CA", "CB", "OG", 1.40, 60.0, 3)],   # χ1
            "THR": [("N", "CA", "CB", "OG1", 1.40, 60.0, 3)],  # χ1
            "CYS": [("N", "CA", "CB", "SG", 1.40, 60.0, 3)],   # χ1
            "MET": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1
                   ("CA", "CB", "CG", "SD", 2.06, 60.0, 3),    # χ2
                   ("CB", "CG", "SD", "CE", 1.70, 60.0, 3)],   # χ3
            "ASP": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1
                   ("CA", "CB", "CG", "OD1", 2.20, 0.0, 2)],   # χ2
            "GLU": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1
                   ("CA", "CB", "CG", "CD", 1.40, 60.0, 3),    # χ2
                   ("CB", "CG", "CD", "OE1", 2.20, 0.0, 2)],   # χ3
            "ASN": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1
                   ("CA", "CB", "CG", "OD1", 2.50, 0.0, 2)],   # χ2
            "GLN": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1
                   ("CA", "CB", "CG", "CD", 1.40, 60.0, 3),    # χ2
                   ("CB", "CG", "CD", "OE1", 2.50, 0.0, 2)],   # χ3
            "LYS": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1
                   ("CA", "CB", "CG", "CD", 1.40, 60.0, 3),    # χ2
                   ("CB", "CG", "CD", "CE", 1.40, 60.0, 3),    # χ3
                   ("CG", "CD", "CE", "NZ", 1.40, 60.0, 3)],   # χ4
            "ARG": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1
                   ("CA", "CB", "CG", "CD", 1.40, 60.0, 3),    # χ2
                   ("CB", "CG", "CD", "NE", 1.40, 60.0, 3),    # χ3
                   ("CG", "CD", "NE", "CZ", 2.40, 0.0, 2)],    # χ4
            "HIS": [("N", "CA", "CB", "CG", 1.40, 90.0, 2),    # χ1
                   ("CA", "CB", "CG", "ND1", 7.4, 0.0, 2)],    # χ2
            "PRO": [("N", "CA", "CB", "CG", 1.40, 60.0, 3),    # χ1 (constrained by ring)
                   ("CA", "CB", "CG", "CD", 1.40, 60.0, 3)],   # χ2 (constrained by ring)
            "GLY": [],  # No side chain
        }
        
        return sidechain_dihedrals.get(residue_name, [])
    
    def _generate_fallback_dihedrals(self, atoms: List[AmberAtom], dihedrals: List[AmberDihedral]) -> None:
        """Generate backbone and side chain dihedrals when cpptraj parsing fails."""
        # Group atoms by residue
        residues = {}
        atom_map = {}  # Map (residue_idx, atom_name) -> atom
        
        for atom in atoms:
            if atom.residue_index not in residues:
                residues[atom.residue_index] = []
            residues[atom.residue_index].append(atom)
            atom_map[(atom.residue_index, atom.name)] = atom
        
        # 1. Generate backbone phi and psi dihedrals
        for res_idx in sorted(residues.keys()):
            if res_idx == 0 or res_idx + 1 not in residues:
                continue  # Skip first and last residues for complete dihedrals
            
            # Current residue atoms
            curr_n = atom_map.get((res_idx, "N"))
            curr_ca = atom_map.get((res_idx, "CA"))
            curr_c = atom_map.get((res_idx, "C"))
            
            # Previous residue C atom
            prev_c = atom_map.get((res_idx - 1, "C"))
            
            # Next residue N atom
            next_n = atom_map.get((res_idx + 1, "N"))
            
            # Phi dihedral: C(i-1) - N(i) - CA(i) - C(i)
            if prev_c and curr_n and curr_ca and curr_c:
                dihedrals.append(AmberDihedral(
                    prev_c.index, curr_n.index, curr_ca.index, curr_c.index,
                    1.5,  # Force constant kcal/mol
                    np.radians(180.0),  # Phase
                    2  # Periodicity
                ))
            
            # Psi dihedral: N(i) - CA(i) - C(i) - N(i+1)
            if curr_n and curr_ca and curr_c and next_n:
                dihedrals.append(AmberDihedral(
                    curr_n.index, curr_ca.index, curr_c.index, next_n.index,
                    1.5,  # Force constant kcal/mol
                    np.radians(180.0),  # Phase
                    2  # Periodicity
                ))
        
        # 2. Generate side chain dihedrals
        for res_idx, res_atoms in residues.items():
            if not res_atoms:
                continue
                
            residue_name = res_atoms[0].residue_name
            sidechain_dihedral_patterns = self._get_sidechain_dihedrals(residue_name)
            # Filter out hydrogen dihedrals for constraint generation  
            non_hydrogen_sidechain_dihedrals = self._filter_hydrogen_dihedrals(sidechain_dihedral_patterns)
            
            for atom1_name, atom2_name, atom3_name, atom4_name, force_constant, phase_deg, periodicity in non_hydrogen_sidechain_dihedrals:
                atom1 = atom_map.get((res_idx, atom1_name))
                atom2 = atom_map.get((res_idx, atom2_name))
                atom3 = atom_map.get((res_idx, atom3_name))
                atom4 = atom_map.get((res_idx, atom4_name))
                
                if atom1 and atom2 and atom3 and atom4:
                    # Convert degrees to radians
                    phase_rad = np.radians(phase_deg)
                    
                    dihedrals.append(AmberDihedral(
                        atom1.index, atom2.index, atom3.index, atom4.index,
                        force_constant, phase_rad, periodicity
                    )) 
    
    def _extract_box_dimensions(self, prmtop_path: Path) -> Optional[Tuple[float, float, float]]:
        """Extract box dimensions if present."""
        commands = ["box"]
        # Extract prefix from topology filename
        output_prefix = prmtop_path.stem
        try:
            output = self._run_cpptraj_command(prmtop_path, commands, output_prefix)
            
            for line in output.split('\n'):
                if "Box:" in line or "box" in line.lower():
                    # Try to extract box dimensions
                    numbers = re.findall(r'[\d.]+', line)
                    if len(numbers) >= 3:
                        return (
                            float(numbers[0]),
                            float(numbers[1]),
                            float(numbers[2])
                        )
        except:
            pass
        
        return None 
"""
Generator for converting Amber force field parameters to Boltz constraints.
"""

import os, sys
import traceback
import warnings
import numpy as np

from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any

from .types import AmberForceFieldParams, AmberTopology
from .tleap_interface import TleapInterface
from .cpptraj_parser import <PERSON><PERSON><PERSON>jParser
from .utils import create_amber_summary_file


class AmberConstraintGenerator:
    """
    Generate Boltz constraints from Amber force field parameters.
    
    This class provides methods to:
    - Generate topology from sequences
    - Convert force field parameters to constraints
    - Handle different constraint types (bonds, angles, dihedrals)
    """
    
    def __init__(
        self,
        amber_home: Optional[str] = None,
        force_field_params: Optional[AmberForceFieldParams] = None,
        constraint_weight_scale: float = 1.0
    ):
        """
        Initialize Amber constraint generator.
        
        Parameters
        ----------
        amber_home : Optional[str]
            Path to Amber installation
        force_field_params : Optional[AmberForceFieldParams]
            Force field parameters
        constraint_weight_scale : float
            Global scaling factor for constraint weights
        """
        self.tleap = TleapInterface(amber_home, force_field_params)
        # CpptrajParser will be initialized with working_dir in generate_constraints method
        self.amber_home = amber_home
        self.constraint_weight_scale = constraint_weight_scale
        self.force_field_params = force_field_params or AmberForceFieldParams()
    
    
    def _is_hydrogen_atom(self, atom_name: str) -> bool:
        """Check if atom is hydrogen based on atom name."""
        # Hydrogen atoms typically start with 'H' (H, HA, HB1, HG2, etc.)
        return atom_name.startswith('H')
    
    def generate_constraints(
        self,
        sequence: str,
        chain_id: str = "A",
        constraint_types: List[str] = ["bonds", "angles"],
        working_dir: Optional[Path] = None,
        solvate: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Generate Boltz constraints from sequence using Amber force field.
        
        Parameters
        ----------
        sequence : str
            Amino acid sequence
        chain_id : str
            Chain identifier
        constraint_types : List[str]
            Types of constraints to generate: "bonds", "angles", "dihedrals"
        working_dir : Optional[Path]
            Working directory for temporary files
        solvate : bool
            Whether to include solvent
            
        Returns
        -------
        List[Dict[str, Any]]
            List of constraint dictionaries compatible with Boltz schema
        """        
        
        try:
            print(f"[INFO ] Generate Amber topology for chain {chain_id} ({len(sequence)} residues)")
            
            print(f'  INFO: running tleap')
            prmtop_path, inpcrd_path = self.tleap.generate_topology(
                sequence=sequence,
                output_prefix=f"chain_{chain_id}",
                chain_id=chain_id,
                solvate=solvate,
                working_dir=working_dir
            )
            
            print(f'  INFO: running cpptraj')
            # Initialize cpptraj parser with working directory for file saving
            cpptraj_working_dir = prmtop_path.parent if working_dir is None else working_dir
            cpptraj = CpptrajParser(self.amber_home, working_dir=cpptraj_working_dir)
            
            print(f'  INFO: parsing topology with cpptraj')            
            topology = cpptraj.parse_topology(prmtop_path)
            
            print(f"[INFO ] Generate constraints for chain {chain_id}")
            # Generate constraints
            constraints = []
            constraint_summary = {}
            
            if "bonds" in constraint_types:
                print(f'  INFO: generating amber-ff bond contraints')
                bond_constraints = self._generate_bond_constraints(topology, chain_id)
                constraints.extend(bond_constraints)
                constraint_summary["bonds"] = {"count": len(bond_constraints),}
                print(f"  INFO: Bond constraints {len(bond_constraints)} is generated")
            
            if "angles" in constraint_types:
                print(f'  INFO: generating amber-ff angle contraints')
                angle_constraints = self._generate_angle_constraints(topology, chain_id)
                constraints.extend(angle_constraints)
                constraint_summary["angles"] = {"count": len(angle_constraints)}
                print(f"  INFO: Angle constraints {len(angle_constraints)} is generated")
            
            if "dihedrals" in constraint_types:
                print(f'  INFO: generating amber-ff dihedral contraints')
                dihedral_constraints = self._generate_dihedral_constraints(topology, chain_id)
                constraints.extend(dihedral_constraints)
                constraint_summary["dihedrals"] = {"count": len(dihedral_constraints), }
                print(f"  INFO: Dihedral constraints {len(dihedral_constraints)} is generated")
            
            # Calculate statistics
            constraint_density = len(constraints) / len(sequence) if sequence else 0
            
            # Create summary data
            summary_data = {
                "sequence": sequence,
                "chain_id": chain_id,
                "sequence_length": len(sequence),
                "force_field": self.force_field_params.force_field_name,
                "constraint_types_requested": constraint_types,
                "constraint_weight_scale": self.constraint_weight_scale,
                "solvated": solvate,
                "topology_summary": {
                    "total_atoms": len(topology.atoms),
                    "total_bonds": len(topology.bonds),
                    "total_angles": len(topology.angles),
                    "total_dihedrals": len(topology.dihedrals),
                    "box_dimensions": topology.box_dimensions
                },
                "constraint_summary": constraint_summary,
                "total_constraints_generated": len(constraints),
                "constraint_density": constraint_density,
                "output_files": {
                    "topology": str(prmtop_path),
                    "coordinates": str(inpcrd_path)
                }
            }
            
            # Create summary file in the working directory
            if working_dir:
                create_amber_summary_file(Path(working_dir), summary_data)
            else:
                # If working_dir is not provided, use the directory where topology files are located
                summary_dir = prmtop_path.parent
                create_amber_summary_file(summary_dir, summary_data)
            
            # Print comprehensive results
            print(f"[INFO ] ✅ Amber constraint generation completed!")
            print(f"  📊 Summary for chain {chain_id}:")
            print(f"      Sequence: {len(sequence)} residues")
            print(f"      Topology: {len(topology.atoms)} atoms, {len(topology.bonds)} bonds, "
                  f"{len(topology.angles)} angles, {len(topology.dihedrals)} dihedrals")
            print(f"      Constraints: {len(constraints)} total ({constraint_density:.1f} per residue)")
            
            return constraints
            
        except Exception as e:
            print(f"[ERROR] Failed to generate Amber constraints: {e}")
            traceback.print_exc()
            warnings.warn(f"Failed to generate Amber constraints: {e}")
            return []
    
    def _generate_bond_constraints(
        self,
        topology: AmberTopology,
        chain_id: str
    ) -> List[Dict[str, Any]]:
        """Generate bond length constraints using actual Amber force field parameters."""
        constraints = []
        
        # Use actual bond information from cpptraj parsing (filter out hydrogen bonds)
        for bond in topology.bonds:
            atom1 = topology.get_atom_by_index(bond.atom1_idx)
            atom2 = topology.get_atom_by_index(bond.atom2_idx)
            
            if atom1 and atom2:
                # Skip hydrogen bonds for Boltz constraint generation
                if self._is_hydrogen_atom(atom1.name) or self._is_hydrogen_atom(atom2.name):
                    continue
                
                constraint = {
                    "amber_bond": {
                        "atom1": [chain_id, atom1.residue_index + 1, atom1.name],  # 1-indexed
                        "atom2": [chain_id, atom2.residue_index + 1, atom2.name],  # 1-indexed
                        "equilibrium_distance": float(bond.equilibrium_distance),  # Store equilibrium distance
                        "force_constant": float(bond.force_constant),  # Store original force constant
                        "weight": float(bond.force_constant)
                    }
                }
                constraints.append(constraint)
        
        return constraints


    def _generate_angle_constraints(
        self,
        topology: AmberTopology,
        chain_id: str
    ) -> List[Dict[str, Any]]:
        """
        Generate enhanced angle constraints using actual Amber force field parameters.
        
        Key improvements:
        - Adaptive tolerance based on force constant
        - Angle type classification (backbone vs side chain)
        - Filtering based on constraint significance
        """
        constraints = []
        angle_stats = {"backbone": 0, "side_chain": 0, "filtered_out": 0}
        
        for angle in topology.angles:
            atom1 = topology.get_atom_by_index(angle.atom1_idx)
            atom2 = topology.get_atom_by_index(angle.atom2_idx)
            atom3 = topology.get_atom_by_index(angle.atom3_idx)
            
            if atom1 and atom2 and atom3:
                # Skip hydrogen angles for Boltz constraint generation
                if (self._is_hydrogen_atom(atom1.name) 
                    or self._is_hydrogen_atom(atom2.name) 
                    or self._is_hydrogen_atom(atom3.name)):
                    angle_stats["filtered_out"] += 1
                    continue
                
                # Classify angle type for specialized processing
                angle_type = self._classify_angle_type(atom1, atom2, atom3)
                angle_stats[angle_type] += 1
                
                # Get actual bond lengths for more accurate distance calculation
                bond_12_length = self._get_bond_length(topology, atom1.index, atom2.index)
                bond_23_length = self._get_bond_length(topology, atom2.index, atom3.index)
                
                # Convert angle to distance using law of cosines with actual bond lengths
                equilibrium_distance = self._angle_to_distance(
                    angle.equilibrium_angle,
                    bond_length_12=bond_12_length,
                    bond_length_23=bond_23_length
                )

                # Generate amber_angle constraint using actual Amber force field parameters
                constraint = {
                    "amber_angle": {
                        "atom1": [chain_id, atom1.residue_index + 1, atom1.name],  # 1-indexed for Boltz
                        "atom2": [chain_id, atom2.residue_index + 1, atom2.name],  # center atom
                        "atom3": [chain_id, atom3.residue_index + 1, atom3.name],  # 1-indexed for Boltz
                        "equilibrium_angle": float(angle.equilibrium_angle),  # radians
                        "equilibrium_distance": float(equilibrium_distance),  # Angstroms (1-3 distance)
                        "force_constant": float(angle.force_constant),  # kcal/mol/rad^2 (original Amber)
                        "weight": float(angle.force_constant),  # Use Amber force constant as weight
                        # Store bond lengths for reference
                        "bond_12_length": float(bond_12_length),  # atom1-atom2 bond length
                        "bond_23_length": float(bond_23_length),  # atom2-atom3 bond length
                        "angle_type": angle_type  # backbone or side_chain classification
                    }
                }
                constraints.append(constraint)

        # Print enhanced statistics
        total_processed = sum(angle_stats.values())
        if total_processed > 0:
            print(f"    Angle Constraints Processing (amber_angle implementation active):")
            print(f"      Backbone angles: {angle_stats['backbone']}")
            print(f"      Side chain angles: {angle_stats['side_chain']}")
            print(f"      Filtered out (weak/hydrogen): {angle_stats['filtered_out']}")
            print(f"      Total constraints generated: {len(constraints)}")

        return constraints
    
    def _classify_angle_type(self, atom1: 'AmberAtom', atom2: 'AmberAtom', atom3: 'AmberAtom') -> str:
        """
        Classify angle as backbone or side chain for specialized processing.
        
        Parameters
        ----------
        atom1, atom2, atom3 : AmberAtom
            The three atoms forming the angle
            
        Returns
        -------
        str
            'backbone' or 'side_chain'
        """
        backbone_atoms = {"N", "CA", "C", "O"}
        
        # Get atom names
        names = {atom1.name, atom2.name, atom3.name}
        
        # If all atoms are backbone atoms, classify as backbone
        if names.issubset(backbone_atoms):
            return "backbone"
        
        # If center atom (atom2) is backbone and connects to side chain, still important
        if atom2.name in backbone_atoms and len(names.intersection(backbone_atoms)) >= 2:
            return "backbone"
        
        return "side_chain"
    
    def _calculate_adaptive_tolerance(self, force_constant: float, angle_type: str) -> float:
        """
        Calculate adaptive tolerance based on force constant and angle type.
        
        Parameters
        ----------
        force_constant : float
            Force constant in kcal/mol/rad^2
        angle_type : str
            'backbone' or 'side_chain'
            
        Returns
        -------
        float
            Tolerance factor (0.03-0.25 range)
        """
        # Base tolerance depends on angle type
        if angle_type == "backbone":
            base_tolerance = 0.08  # Tighter for backbone (8%)
        else:
            base_tolerance = 0.12  # Looser for side chain (12%)
        
        # Adjust based on force constant
        # Higher force constant → tighter tolerance
        # Lower force constant → looser tolerance
        if force_constant > 80.0:  # Very strong
            adjustment = 0.6  # Make tighter
        elif force_constant > 50.0:  # Strong
            adjustment = 0.8
        elif force_constant > 30.0:  # Medium
            adjustment = 1.0  # No change
        elif force_constant > 15.0:  # Weak
            adjustment = 1.3
        else:  # Very weak
            adjustment = 1.5  # Make looser
        
        tolerance = base_tolerance * adjustment
        
        # Clamp to reasonable range
        return min(max(tolerance, 0.03), 0.25)
    
    def _calculate_enhanced_angle_weight(self, force_constant: float, angle_type: str) -> float:
        """
        Calculate enhanced weight based on force constant and angle type.
        
        Parameters
        ----------
        force_constant : float
            Force constant in kcal/mol/rad^2
        angle_type : str
            'backbone' or 'side_chain'
            
        Returns
        -------
        float
            Enhanced weight (0.1-1.0 range)
        """
        # Normalize force constant for angles (typical range: 20-100 kcal/mol/rad^2)
        normalized_fc = force_constant / 70.0  # Typical strong angle force constant
        
        # Apply angle type multiplier
        if angle_type == "backbone":
            type_multiplier = 1.2  # Backbone angles are more important
        else:
            type_multiplier = 0.9   # Side chain angles slightly less important
        
        # Enhanced sigmoid transformation for better weight distribution
        sigmoid_input = normalized_fc * type_multiplier
        weight = (2.0 / (1.0 + np.exp(-2.0 * sigmoid_input))) - 1.0  # Enhanced sigmoid range [0,1]
        
        # Apply final scaling and clamping
        weight = weight * 0.7  # Scale down from bond weights
        return min(max(weight, 0.1), 0.95)
    
    def _generate_dihedral_constraints(
        self,
        topology: AmberTopology,
        chain_id: str
    ) -> List[Dict[str, Any]]:
        """
        Generate enhanced dihedral constraints using actual Amber force field parameters.
        
        Key improvements:
        - Dihedral type classification (φ/ψ vs χ1/χ2/χ3/χ4)
        - Ramachandran plot based distance calculation for φ/ψ
        - Enhanced periodicity and phase utilization
        - Improved force constant filtering and weight calculation
        """
        constraints = []
        dihedral_stats = {"phi_psi": 0, "side_chain": 0, "other": 0, "filtered_out": 0}
        
        for dihedral in topology.dihedrals:
            atom1 = topology.get_atom_by_index(dihedral.atom1_idx)
            atom2 = topology.get_atom_by_index(dihedral.atom2_idx)
            atom3 = topology.get_atom_by_index(dihedral.atom3_idx)
            atom4 = topology.get_atom_by_index(dihedral.atom4_idx)
            
            if atom1 and atom2 and atom3 and atom4:
                # Skip hydrogen dihedrals for Boltz constraint generation
                if (self._is_hydrogen_atom(atom1.name) or self._is_hydrogen_atom(atom2.name) or 
                    self._is_hydrogen_atom(atom3.name) or self._is_hydrogen_atom(atom4.name)):
                    dihedral_stats["filtered_out"] += 1
                    continue
                
                # Enhanced force constant filtering based on dihedral type
                dihedral_type = self._classify_dihedral_type(atom1, atom2, atom3, atom4)
                min_force_constant = self._get_min_force_constant_for_dihedral_type(dihedral_type)
                
                if dihedral.force_constant < min_force_constant:
                    dihedral_stats["filtered_out"] += 1
                    continue
                
                dihedral_stats[dihedral_type] += 1
                
                # Calculate enhanced 1-4 distance based on dihedral type and parameters
                distance = self._calculate_enhanced_14_distance(
                    topology, atom1, atom2, atom3, atom4, dihedral, dihedral_type
                )
                
                # Calculate adaptive tolerance based on dihedral type and force constant
                tolerance = self._calculate_dihedral_tolerance(dihedral.force_constant, dihedral_type)
                
                # Calculate enhanced weight based on force constant and dihedral type
                weight = self._calculate_enhanced_dihedral_weight(dihedral.force_constant, dihedral_type)
                
                # Apply bounds with adaptive tolerance
                lower_bound = distance * (1 - tolerance)
                upper_bound = distance * (1 + tolerance)
                
                # TODO: Generate amber_dihedral constraint here (future implementation)
                # constraint = {
                #     "amber_dihedral": {
                #         "atom1": [chain_id, atom1.residue_index + 1, atom1.name],
                #         "atom2": [chain_id, atom2.residue_index + 1, atom2.name],
                #         "atom3": [chain_id, atom3.residue_index + 1, atom3.name],
                #         "atom4": [chain_id, atom4.residue_index + 1, atom4.name],
                #         "phase": float(dihedral.phase),
                #         "periodicity": int(dihedral.periodicity),
                #         "force_constant": float(dihedral.force_constant),
                #         "weight": float(weight * self.constraint_weight_scale)
                #     }
                # }
                # constraints.append(constraint)
        
        # Print enhanced statistics
        total_processed = sum(dihedral_stats.values())
        if total_processed > 0:
            print(f"    Dihedral Constraints Processing (amber_dihedral implementation pending):")
            print(f"      φ/ψ backbone dihedrals: {dihedral_stats['phi_psi']}")
            print(f"      Side chain dihedrals: {dihedral_stats['side_chain']}")
            print(f"      Other dihedrals: {dihedral_stats['other']}")
            print(f"      Filtered out (weak/hydrogen): {dihedral_stats['filtered_out']}")
            print(f"      Total constraints: {len(constraints)} (currently disabled)")
        
        return constraints
    
    def _classify_dihedral_type(self, atom1: 'AmberAtom', atom2: 'AmberAtom', 
                               atom3: 'AmberAtom', atom4: 'AmberAtom') -> str:
        """
        Classify dihedral type for specialized processing.
        
        Parameters
        ----------
        atom1, atom2, atom3, atom4 : AmberAtom
            The four atoms forming the dihedral
            
        Returns
        -------
        str
            'phi_psi', 'side_chain', or 'other'
        """
        # Check for φ/ψ backbone dihedrals
        # φ: C(i-1) - N(i) - CA(i) - C(i)
        # ψ: N(i) - CA(i) - C(i) - N(i+1)
        
        atom_names = [atom1.name, atom2.name, atom3.name, atom4.name]
        
        # φ dihedral pattern: C - N - CA - C
        if (atom_names == ["C", "N", "CA", "C"] and 
            atom1.residue_index == atom2.residue_index - 1 and
            atom2.residue_index == atom3.residue_index == atom4.residue_index):
            return "phi_psi"
        
        # ψ dihedral pattern: N - CA - C - N  
        if (atom_names == ["N", "CA", "C", "N"] and
            atom1.residue_index == atom2.residue_index == atom3.residue_index and
            atom3.residue_index == atom4.residue_index - 1):
            return "phi_psi"
        
        # Side chain dihedrals (χ1, χ2, χ3, χ4)
        backbone_atoms = {"N", "CA", "C", "O"}
        if (atom2.name in backbone_atoms or atom3.name in backbone_atoms) and not all(name in backbone_atoms for name in atom_names):
            return "side_chain"
        
        return "other"
    
    def _get_min_force_constant_for_dihedral_type(self, dihedral_type: str) -> float:
        """
        Get minimum force constant threshold based on dihedral type.
        
        Parameters
        ----------
        dihedral_type : str
            Type of dihedral ('phi_psi', 'side_chain', 'other')
            
        Returns
        -------
        float
            Minimum force constant threshold
        """
        thresholds = {
            "phi_psi": 0.8,      # φ/ψ dihedrals are crucial for backbone conformation
            "side_chain": 0.5,   # χ dihedrals are important for side chain conformation
            "other": 1.0         # Other dihedrals need higher threshold
        }
        return thresholds.get(dihedral_type, 1.0)
    
    def _calculate_enhanced_14_distance(self, topology: AmberTopology, 
                                       atom1: 'AmberAtom', atom2: 'AmberAtom', 
                                       atom3: 'AmberAtom', atom4: 'AmberAtom',
                                       dihedral: 'AmberDihedral', dihedral_type: str) -> float:
        """
        Calculate enhanced 1-4 distance using dihedral type specific methods.
        
        Parameters
        ----------
        topology : AmberTopology
            Topology information
        atom1, atom2, atom3, atom4 : AmberAtom
            The four atoms forming the dihedral
        dihedral : AmberDihedral
            Dihedral parameters
        dihedral_type : str
            Type of dihedral
            
        Returns
        -------
        float
            Enhanced 1-4 distance estimate
        """
        if dihedral_type == "phi_psi":
            return self._calculate_phi_psi_distance(dihedral.phase, dihedral.periodicity)
        elif dihedral_type == "side_chain":
            return self._calculate_side_chain_dihedral_distance(
                topology, atom1, atom2, atom3, atom4, dihedral
            )
        else:
            return self._estimate_14_distance(
                topology, atom1.index, atom2.index, atom3.index, atom4.index,
                dihedral.phase, dihedral.periodicity
            )
    
    def _calculate_phi_psi_distance(self, phase: float, periodicity: int) -> float:
        """
        Calculate 1-4 distance for φ/ψ dihedrals based on Ramachandran plot regions.
        
        Parameters
        ----------
        phase : float
            Dihedral phase in radians
        periodicity : int
            Dihedral periodicity
            
        Returns
        -------
        float
            1-4 distance for φ/ψ dihedral
        """
        # Convert phase to degrees for easier interpretation
        phase_deg = np.degrees(phase) % 360
        if phase_deg > 180:
            phase_deg -= 360
        
        # Ramachandran plot based distance estimation
        # α-helix region (φ ≈ -60°, ψ ≈ -45°): shorter 1-4 distance
        # β-sheet region (φ ≈ -120°, ψ ≈ +120°): longer 1-4 distance
        # Extended region: longest 1-4 distance
        
        if periodicity == 2:  # Typical for φ/ψ dihedrals
            if -90 <= phase_deg <= -30:  # α-helix like
                return 2.8  # Shorter distance
            elif 90 <= phase_deg <= 150 or -150 <= phase_deg <= -90:  # β-sheet like
                return 3.2  # Medium distance
            else:  # Extended or other conformations
                return 3.6  # Longer distance
        
        return 3.2  # Default for φ/ψ
    
    def _calculate_side_chain_dihedral_distance(self, topology: AmberTopology,
                                               atom1: 'AmberAtom', atom2: 'AmberAtom',
                                               atom3: 'AmberAtom', atom4: 'AmberAtom',
                                               dihedral: 'AmberDihedral') -> float:
        """
        Calculate 1-4 distance for side chain dihedrals (χ angles).
        
        Parameters
        ----------
        topology : AmberTopology
            Topology information
        atom1, atom2, atom3, atom4 : AmberAtom
            The four atoms forming the dihedral
        dihedral : AmberDihedral
            Dihedral parameters
            
        Returns
        -------
        float
            1-4 distance for side chain dihedral
        """
        # Get actual bond lengths for more accurate calculation
        bond_12 = self._get_bond_length(topology, atom1.index, atom2.index)
        bond_23 = self._get_bond_length(topology, atom2.index, atom3.index)
        bond_34 = self._get_bond_length(topology, atom3.index, atom4.index)
        
        # Get angles if available
        angle_123 = self._get_angle_value(topology, atom1.index, atom2.index, atom3.index)
        angle_234 = self._get_angle_value(topology, atom2.index, atom3.index, atom4.index)
        
        # Calculate 1-4 distance using enhanced geometry
        # This uses more sophisticated vector calculations
        distance_13 = self._angle_to_distance(angle_123, bond_12, bond_23)
        
        # Apply dihedral-dependent correction
        cos_dihedral = np.cos(dihedral.phase)
        
        # Enhanced calculation considering dihedral angle
        # For gauche conformations (±60°): shorter distance
        # For trans conformations (180°): longer distance
        if dihedral.periodicity == 3:  # Common for χ1 dihedrals
            if abs(np.cos(dihedral.phase)) < 0.5:  # Near ±60° (gauche)
                distance_14 = np.sqrt(distance_13**2 + bond_34**2 - 
                                    2*distance_13*bond_34*np.cos(angle_234)*0.7)  # Gauche correction
            else:  # Trans-like
                distance_14 = np.sqrt(distance_13**2 + bond_34**2 - 
                                    2*distance_13*bond_34*np.cos(angle_234))
        else:
            # Standard calculation for other periodicities
            distance_14 = np.sqrt(distance_13**2 + bond_34**2 - 
                                2*distance_13*bond_34*np.cos(angle_234)*cos_dihedral*0.5)
        
        return max(distance_14, 2.2)  # Minimum for side chain 1-4 interactions
    
    def _angle_to_distance(
        self,
        angle: float,
        bond_length_12: float = 1.5,
        bond_length_23: float = 1.5
    ) -> float:
        """
        Convert angle to 1-3 distance using law of cosines.
        
        Parameters
        ----------
        angle : float
            Angle in radians
        bond_length_12 : float
            Length of bond between atoms 1 and 2
        bond_length_23 : float
            Length of bond between atoms 2 and 3
            
        Returns
        -------
        float
            Distance between atoms 1 and 3
        """
        # Law of cosines: c^2 = a^2 + b^2 - 2ab*cos(C)
        distance_squared = (
            bond_length_12**2 + bond_length_23**2 - 
            2 * bond_length_12 * bond_length_23 * np.cos(angle)
        )
        return np.sqrt(distance_squared)
    
    def _dihedral_to_distance(
        self,
        phase: float,
        periodicity: int
    ) -> float:
        """
        Estimate 1-4 distance based on dihedral parameters.
        
        Parameters
        ----------
        phase : float
            Dihedral phase in radians
        periodicity : int
            Dihedral periodicity
            
        Returns
        -------
        float
            Estimated 1-4 distance
        """
        # This is a rough approximation
        # For extended conformation (trans): ~3.8 Å
        # For gauche: ~2.9 Å
        # For cis: ~2.5 Å
        
        if periodicity == 3:  # Typical for sp3-sp3 bonds
            if abs(phase) < np.pi/3:  # gauche-like
                return 2.9
            else:  # trans-like
                return 3.8
        else:  # Default
            return 3.5
    
    def generate_multichain_constraints(
        self,
        chain_sequences: Dict[str, str],
        constraint_types: List[str] = ["bonds", "angles"],
        working_dir: Optional[Path] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate constraints for multiple chains.
        
        Parameters
        ----------
        chain_sequences : Dict[str, str]
            Dictionary mapping chain IDs to sequences
        constraint_types : List[str]
            Types of constraints to generate
        working_dir : Optional[Path]
            Working directory
            
        Returns
        -------
        List[Dict[str, Any]]
            Combined list of constraints for all chains
        """
        all_constraints = []
        
        for chain_id, sequence in chain_sequences.items():
            constraints = self.generate_constraints(
                sequence=sequence,
                chain_id=chain_id,
                constraint_types=constraint_types,
                working_dir=working_dir
            )
            all_constraints.extend(constraints)
        
        print(f"  INFO: Generated {len(all_constraints)} total Amber constraints")
        return all_constraints 
    
    def _get_bond_length(self, topology: AmberTopology, atom1_idx: int, atom2_idx: int) -> float:
        """Get bond length between two atoms from topology, or return default."""
        # Search for bond between atoms
        for bond in topology.bonds:
            if ((bond.atom1_idx == atom1_idx and bond.atom2_idx == atom2_idx) or
                (bond.atom1_idx == atom2_idx and bond.atom2_idx == atom1_idx)):
                return bond.equilibrium_distance
        
        # If bond not found, return default based on atom types
        atom1 = topology.get_atom_by_index(atom1_idx)
        atom2 = topology.get_atom_by_index(atom2_idx)
        
        if atom1 and atom2:
            # Default bond lengths based on atom names
            if (atom1.name, atom2.name) in [("N", "CA"), ("CA", "N")]:
                return 1.449
            elif (atom1.name, atom2.name) in [("CA", "C"), ("C", "CA")]:
                return 1.522
            elif (atom1.name, atom2.name) in [("C", "N"), ("N", "C")]:
                return 1.335
            elif (atom1.name, atom2.name) in [("C", "O"), ("O", "C")]:
                return 1.229
        
        return 1.5  # Default fallback
    
    def _estimate_14_distance(
        self, 
        topology: AmberTopology,
        atom1_idx: int, atom2_idx: int, atom3_idx: int, atom4_idx: int,
        phase: float, periodicity: int
    ) -> float:
        """Estimate 1-4 distance using actual bond lengths and dihedral parameters."""
        # Get actual bond lengths
        bond_12 = self._get_bond_length(topology, atom1_idx, atom2_idx)
        bond_23 = self._get_bond_length(topology, atom2_idx, atom3_idx)
        bond_34 = self._get_bond_length(topology, atom3_idx, atom4_idx)
        
        # Get angles if available, or use defaults
        angle_123 = self._get_angle_value(topology, atom1_idx, atom2_idx, atom3_idx)
        angle_234 = self._get_angle_value(topology, atom2_idx, atom3_idx, atom4_idx)
        
        # Calculate 1-4 distance using dihedral geometry
        # This is an approximation based on the dihedral phase and periodicity
        cos_dihedral = np.cos(phase)
        
        # Use vector calculations for 1-4 distance
        # This is a simplified calculation - for full accuracy would need 3D coordinates
        distance_13 = self._angle_to_distance(angle_123, bond_12, bond_23)
        distance_24 = self._angle_to_distance(angle_234, bond_23, bond_34)
        
        # Estimate 1-4 distance based on geometry
        # For trans (phase ≈ π): longer distance
        # For cis (phase ≈ 0): shorter distance
        if abs(phase) > np.pi/2:  # trans-like
            distance_14 = np.sqrt(distance_13**2 + bond_34**2 - 2*distance_13*bond_34*np.cos(angle_234))
        else:  # cis-like or gauche
            distance_14 = np.sqrt(distance_13**2 + bond_34**2 + 2*distance_13*bond_34*np.cos(angle_234))
        
        return max(distance_14, 2.0)  # Minimum 2.0 Å for physical constraint
    
    def _get_angle_value(self, topology: AmberTopology, atom1_idx: int, atom2_idx: int, atom3_idx: int) -> float:
        """Get angle value from topology, or return default."""
        # Search for angle between atoms
        for angle in topology.angles:
            if ((angle.atom1_idx == atom1_idx and angle.atom2_idx == atom2_idx and angle.atom3_idx == atom3_idx) or
                (angle.atom1_idx == atom3_idx and angle.atom2_idx == atom2_idx and angle.atom3_idx == atom1_idx)):
                return angle.equilibrium_angle
        
        return np.radians(109.5)  # Default tetrahedral angle 
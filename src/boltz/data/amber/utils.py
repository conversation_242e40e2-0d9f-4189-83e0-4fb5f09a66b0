"""
Utility functions for Amber force field integration.
"""

import os
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any
import yaml
import json


def check_amber_installation() -> bool:
    """
    Check if Amber is properly installed and accessible.
    
    Returns
    -------
    bool
        True if Amber is installed and accessible
    """
    amber_home = os.environ.get("AMBERHOME")
    if not amber_home:
        return False
    
    # Check for essential binaries
    required_binaries = ["tleap", "cpptraj"]
    for binary in required_binaries:
        binary_path = Path(amber_home) / "bin" / binary
        if not binary_path.exists():
            return False
    
    return True


def integrate_amber_constraints_to_schema(
    schema_data: Dict[str, Any],
    amber_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Integrate Amber constraint generation into Boltz schema.
    
    Parameters
    ----------
    schema_data : Dict[str, Any]
        Original Boltz schema data
    amber_params : Optional[Dict[str, Any]]
        Amber-specific parameters
        
    Returns
    -------
    Dict[str, Any]
        Updated schema with Amber constraint integration
    """
    if amber_params is None:
        amber_params = {
            "force_field": "ff14SB",
            "constraint_types": ["bonds", "angles"],
            "weight_scale": 1.0
        }
    
    # Add amber section to sequences if requested
    for seq_item in schema_data.get("sequences", []):
        if "protein" in seq_item:
            protein_data = seq_item["protein"]
            
            # Check if amber constraints are requested
            if protein_data.get("use_amber_constraints", False):
                protein_data["amber"] = amber_params
    
    return schema_data


def merge_constraints(
    existing_constraints: List[Dict[str, Any]],
    amber_constraints: List[Dict[str, Any]],
    merge_strategy: str = "append"
) -> List[Dict[str, Any]]:
    """
    Merge Amber constraints with existing constraints.
    
    Parameters
    ----------
    existing_constraints : List[Dict[str, Any]]
        Existing constraints from schema or templates
    amber_constraints : List[Dict[str, Any]]
        Amber-generated constraints
    merge_strategy : str
        Strategy for merging: "append", "replace", "weighted_average"
        
    Returns
    -------
    List[Dict[str, Any]]
        Merged constraint list
    """
    if merge_strategy == "append":
        # Simply append Amber constraints
        return existing_constraints + amber_constraints
    
    elif merge_strategy == "replace":
        # Replace existing with Amber constraints
        return amber_constraints
    
    elif merge_strategy == "weighted_average":
        # Create a map of existing constraints by atom pairs
        constraint_map = {}
        
        for constraint in existing_constraints:
            if "nmr_distance" in constraint:
                data = constraint["nmr_distance"]
                atom1 = tuple(data["atom1"])
                atom2 = tuple(data["atom2"])
                key = (min(atom1, atom2), max(atom1, atom2))
                
                if key not in constraint_map:
                    constraint_map[key] = []
                constraint_map[key].append(constraint)
        
        # Add Amber constraints, averaging if duplicates exist
        for amber_constraint in amber_constraints:
            if "nmr_distance" in amber_constraint:
                data = amber_constraint["nmr_distance"]
                atom1 = tuple(data["atom1"])
                atom2 = tuple(data["atom2"])
                key = (min(atom1, atom2), max(atom1, atom2))
                
                if key in constraint_map:
                    # Average the bounds and weights
                    existing = constraint_map[key][0]["nmr_distance"]
                    amber_data = amber_constraint["nmr_distance"]
                    
                    averaged_constraint = {
                        "nmr_distance": {
                            "atom1": list(atom1) if atom1 < atom2 else list(atom2),
                            "atom2": list(atom2) if atom1 < atom2 else list(atom1),
                            "lower_bound": (existing["lower_bound"] + amber_data["lower_bound"]) / 2,
                            "upper_bound": (existing["upper_bound"] + amber_data["upper_bound"]) / 2,
                            "weight": (existing.get("weight", 1.0) + amber_data.get("weight", 1.0)) / 2
                        }
                    }
                    constraint_map[key] = [averaged_constraint]
                else:
                    constraint_map[key] = [amber_constraint]
        
        # Flatten the map back to a list
        merged = []
        for constraints in constraint_map.values():
            merged.extend(constraints)
        
        return merged
    
    else:
        raise ValueError(f"Unknown merge strategy: {merge_strategy}")


def cleanup_temp_files(working_dir: Path, force_cleanup: bool = False) -> None:
    """
    Clean up temporary files created during Amber processing.
    
    Parameters
    ----------
    working_dir : Path
        Directory containing files to potentially clean up
    force_cleanup : bool
        If True, forces cleanup even for non-temporary directories.
        Use with caution as this will delete amber_output directories.
    """
    if not working_dir.exists() or not working_dir.is_dir():
        return
    
    # Get temp directory path for comparison
    temp_dir = Path(tempfile.gettempdir())
    
    # Check if this is a temporary directory
    is_temp_dir = str(working_dir).startswith(str(temp_dir))
    
    # Check if this is an amber_output directory
    is_amber_output = "amber_output" in str(working_dir)
    
    if is_temp_dir:
        # Safe to clean up temporary directories
        try:
            shutil.rmtree(working_dir)
            print(f"  INFO: Cleaned up temporary directory: {working_dir}")
        except Exception as e:
            print(f"  WARNING: Failed to clean up temporary directory {working_dir}: {e}")
    
    elif is_amber_output and force_cleanup:
        # Only clean amber_output directories if explicitly requested
        try:
            shutil.rmtree(working_dir)
            print(f"  INFO: Force cleaned up amber output directory: {working_dir}")
        except Exception as e:
            print(f"  WARNING: Failed to clean up amber output directory {working_dir}: {e}")
    
    elif is_amber_output:
        # Keep amber_output directories by default for user inspection
        print(f"  INFO: Amber files preserved at: {working_dir}")
        print(f"        Use force_cleanup=True if you want to remove this directory")
    
    else:
        # Unknown directory type - don't clean up for safety
        print(f"  INFO: Skipping cleanup for non-temporary directory: {working_dir}")


def create_amber_summary_file(working_dir: Path, summary_data: Dict[str, Any]) -> None:
    """
    Create a summary file in the amber output directory for user reference.
    
    Parameters
    ----------
    working_dir : Path
        Amber working directory
    summary_data : Dict[str, Any]
        Summary information to write
    """
    summary_path = working_dir / "amber_summary.json"
    try:
        with summary_path.open("w") as f:
            json.dump(summary_data, f, indent=2)
        print(f"  INFO: Amber summary written to: {summary_path}")
    except Exception as e:
        print(f"  WARNING: Failed to write amber summary: {e}")


def validate_amber_sequence(sequence: str) -> bool:
    """
    Validate if sequence is compatible with Amber force fields.
    
    Parameters
    ----------
    sequence : str
        Amino acid sequence
        
    Returns
    -------
    bool
        True if sequence is valid for Amber
    """
    valid_residues = set("ACDEFGHIKLMNPQRSTVWY")
    return all(aa in valid_residues for aa in sequence.upper())


def estimate_constraint_count(
    sequence: str,
    constraint_types: List[str] = ["bonds", "angles"]
) -> Dict[str, int]:
    """
    Estimate the number of constraints that will be generated.
    
    Parameters
    ----------
    sequence : str
        Amino acid sequence
    constraint_types : List[str]
        Types of constraints to estimate
        
    Returns
    -------
    Dict[str, int]
        Estimated counts by constraint type
    """
    n_residues = len(sequence)
    estimates = {}
    
    if "bonds" in constraint_types:
        # Rough estimate: ~15 bonds per residue
        estimates["bonds"] = n_residues * 15
    
    if "angles" in constraint_types:
        # Rough estimate: ~25 angles per residue
        estimates["angles"] = n_residues * 25
    
    if "dihedrals" in constraint_types:
        # Rough estimate: ~35 dihedrals per residue
        estimates["dihedrals"] = n_residues * 35
    
    estimates["total"] = sum(estimates.values())
    
    return estimates 
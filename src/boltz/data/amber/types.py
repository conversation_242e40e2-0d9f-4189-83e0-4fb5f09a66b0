"""
Amber force field specific data types and constraints.
"""

import numpy as np
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from pathlib import Path

# Define numpy dtypes for Amber constraints
AmberBondConstraint = [
    ("atom_1", np.dtype("i4")),
    ("atom_2", np.dtype("i4")),
    ("force_constant", np.dtype("f4")),  # kcal/mol/A^2
    ("equilibrium_distance", np.dtype("f4")),  # Angstroms
]

AmberAngleConstraint = [
    ("atom_1", np.dtype("i4")),
    ("atom_2", np.dtype("i4")),
    ("atom_3", np.dtype("i4")),
    ("force_constant", np.dtype("f4")),  # kcal/mol/rad^2
    ("equilibrium_angle", np.dtype("f4")),  # radians
]

AmberDihedralConstraint = [
    ("atom_1", np.dtype("i4")),
    ("atom_2", np.dtype("i4")),
    ("atom_3", np.dtype("i4")),
    ("atom_4", np.dtype("i4")),
    ("force_constant", np.dtype("f4")),  # kcal/mol
    ("phase", np.dtype("f4")),  # radians
    ("periodicity", np.dtype("i4")),  # n
]

AmberNonbondedConstraint = [
    ("atom_1", np.dtype("i4")),
    ("atom_2", np.dtype("i4")),
    ("epsilon", np.dtype("f4")),  # kcal/mol
    ("sigma", np.dtype("f4")),  # Angstroms
    ("charge_1", np.dtype("f4")),  # e
    ("charge_2", np.dtype("f4")),  # e
]


@dataclass
class AmberAtom:
    """Represents an atom in Amber topology."""
    index: int
    name: str
    type: str
    charge: float
    mass: float
    residue_index: int
    residue_name: str


@dataclass
class AmberBond:
    """Represents a bond in Amber topology."""
    atom1_idx: int
    atom2_idx: int
    force_constant: float
    equilibrium_distance: float


@dataclass
class AmberAngle:
    """Represents an angle in Amber topology."""
    atom1_idx: int
    atom2_idx: int
    atom3_idx: int
    force_constant: float
    equilibrium_angle: float


@dataclass
class AmberDihedral:
    """Represents a dihedral in Amber topology."""
    atom1_idx: int
    atom2_idx: int
    atom3_idx: int
    atom4_idx: int
    force_constant: float
    phase: float
    periodicity: int


@dataclass
class AmberTopology:
    """Complete Amber topology information."""
    atoms: List[AmberAtom]
    bonds: List[AmberBond]
    angles: List[AmberAngle]
    dihedrals: List[AmberDihedral]
    box_dimensions: Optional[Tuple[float, float, float]] = None
    
    def get_atom_by_index(self, idx: int) -> Optional[AmberAtom]:
        """Get atom by its index."""
        for atom in self.atoms:
            if atom.index == idx:
                return atom
        return None
    
    def get_residue_atoms(self, residue_idx: int) -> List[AmberAtom]:
        """Get all atoms in a specific residue."""
        return [atom for atom in self.atoms if atom.residue_index == residue_idx]


@dataclass
class AmberForceFieldParams:
    """Amber force field parameters."""
    force_field_name: str = "ff14SB"
    water_model: str = "TIP3P"
    salt_concentration: float = 0.15  # M
    neutralize: bool = True
    box_padding: float = 10.0  # Angstroms
    
    def get_tleap_commands(self) -> List[str]:
        """Generate tleap command list based on parameters."""
        commands = []
        
        # For protein force fields
        if self.force_field_name in ["ff14SB", "ff19SB", "ff99SB"]:
            commands.append(f"source leaprc.protein.{self.force_field_name}")
        else:
            commands.append(f"source leaprc.{self.force_field_name}")
        
        # For water model
        commands.append(f"source leaprc.water.{self.water_model.lower()}")
        
        return commands 